# Queue Status Logging Implementation

## Overview
Implemented comprehensive queue status logging that provides periodic visibility into the upload queue state, helping with monitoring, troubleshooting, and performance analysis. The service now logs detailed queue information every 30 seconds (configurable) to both log files and database.

## Key Features Implemented

### 1. Periodic Queue Status Logging
**Purpose**: Provide regular visibility into upload queue state and system health

**Default Interval**: 30 seconds (configurable)

**Information Logged**:
- Queue size and pending upload count
- Individual queue items with wait times and status
- Ready vs waiting item counts
- Consecutive failure tracking
- Processing pause status
- Deleted trigger file tracking
- Locked file counts

### 2. Detailed Queue Item Information
**For each queued item, logs**:
- Trigger file name
- Target folder path
- Current status (waiting/ready)
- Remaining wait time
- Position in queue

### 3. System Health Monitoring
**Tracks key operational metrics**:
- Processing pause state
- Consecutive failure counts
- File locking status
- Deleted trigger file tracking

## Implementation Details

### Configuration Options
```xml
<!-- Queue Status Logging Settings -->
<!-- Enable periodic queue status logging (recommended: true) -->
<add key="EnableQueueStatusLogging" value="true" />

<!-- Queue status logging interval in seconds (default: 30) -->
<add key="QueueStatusIntervalSeconds" value="30" />
```

### Core Components

#### 1. Timer Setup
```csharp
private Timer queueStatusTimer;
private bool enableQueueStatusLogging;
private int queueStatusIntervalSeconds;

private void SetupQueueStatusTimer()
{
    try
    {
        queueStatusTimer = new Timer(queueStatusIntervalSeconds * 1000);
        queueStatusTimer.Elapsed += OnQueueStatusTimerElapsed;
        queueStatusTimer.AutoReset = true;
        queueStatusTimer.Start();

        LogInfo($"Queue status timer configured with {queueStatusIntervalSeconds} second interval");
    }
    catch (Exception ex)
    {
        LogError($"Error setting up queue status timer: {ex.Message}", ex);
        throw;
    }
}
```

#### 2. Queue Status Event Handler
```csharp
private void OnQueueStatusTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
{
    try
    {
        LogQueueStatus();
    }
    catch (Exception ex)
    {
        LogError($"Error during queue status logging: {ex.Message}", ex);
    }
}
```

#### 3. Comprehensive Queue Status Logging
```csharp
private void LogQueueStatus()
{
    try
    {
        int queueCount = uploadQueue.Count;
        
        if (queueCount == 0)
        {
            LogInfo("QUEUE STATUS: Empty - No uploads pending");
            return;
        }

        LogInfo($"QUEUE STATUS: {queueCount} upload(s) pending");

        // Get queue items for detailed logging
        var queueItems = uploadQueue.ToArray();
        
        for (int i = 0; i < Math.Min(queueItems.Length, 10); i++) // Show up to 10 items
        {
            var item = queueItems[i];
            var waitTime = DateTime.Now - item.DetectionTime;
            var remainingWait = waitTimeBeforeUpload - waitTime;
            
            string status;
            if (remainingWait > TimeSpan.Zero)
            {
                status = $"Waiting {remainingWait.TotalMinutes:F1}m";
            }
            else
            {
                status = "Ready for upload";
            }
            
            LogInfo($"  [{i + 1}] {Path.GetFileName(item.TriggerFilePath)} -> {item.FolderPath} ({status})");
        }
        
        if (queueItems.Length > 10)
        {
            LogInfo($"  ... and {queueItems.Length - 10} more items");
        }

        // Log queue statistics
        var readyCount = queueItems.Count(item => (DateTime.Now - item.DetectionTime) >= waitTimeBeforeUpload);
        var waitingCount = queueCount - readyCount;
        
        LogInfo($"QUEUE SUMMARY: {readyCount} ready, {waitingCount} waiting, {consecutiveFailureCount} consecutive failures");
        
        if (processingPaused)
        {
            LogWarn("QUEUE STATUS: Processing is PAUSED due to consecutive failures");
        }

        // Log deleted trigger files tracking
        int deletedCount;
        lock (deletedTriggerFilesLock)
        {
            deletedCount = deletedTriggerFiles.Count;
        }
        
        if (deletedCount > 0)
        {
            LogInfo($"QUEUE STATUS: {deletedCount} deleted trigger file(s) being tracked");
        }

        // Log locked files count
        int lockedCount = lockedTriggerFiles.Count;
        if (lockedCount > 0)
        {
            LogInfo($"QUEUE STATUS: {lockedCount} trigger file(s) currently locked");
        }

        // Log to database if enabled
        _ = Task.Run(async () =>
        {
            try
            {
                await LogServiceEventAsync("QUEUE_STATUS", 
                    $"Queue status: {queueCount} pending, {readyCount} ready, {waitingCount} waiting",
                    $"ConsecutiveFailures: {consecutiveFailureCount}, ProcessingPaused: {processingPaused}, " +
                    $"DeletedTracking: {deletedCount}, LockedFiles: {lockedCount}");
            }
            catch (Exception ex)
            {
                LogError($"Failed to log queue status to database: {ex.Message}", ex);
            }
        });
    }
    catch (Exception ex)
    {
        LogError($"Error generating queue status: {ex.Message}", ex);
    }
}
```

## Sample Log Output

### Empty Queue
```
2024-01-15 10:30:00 [INFO] QUEUE STATUS: Empty - No uploads pending
```

### Queue with Pending Items
```
2024-01-15 10:30:30 [INFO] QUEUE STATUS: 3 upload(s) pending
2024-01-15 10:30:30 [INFO]   [1] batch001.Rdy -> C:\Data\Batch001 (Waiting 15.2m)
2024-01-15 10:30:30 [INFO]   [2] batch002.Rdy -> C:\Data\Batch002 (Waiting 8.7m)
2024-01-15 10:30:30 [INFO]   [3] batch003.Rdy -> C:\Data\Batch003 (Ready for upload)
2024-01-15 10:30:30 [INFO] QUEUE SUMMARY: 1 ready, 2 waiting, 0 consecutive failures
```

### Queue with System Issues
```
2024-01-15 10:31:00 [INFO] QUEUE STATUS: 5 upload(s) pending
2024-01-15 10:31:00 [INFO]   [1] batch001.Rdy -> C:\Data\Batch001 (Ready for upload)
2024-01-15 10:31:00 [INFO]   [2] batch002.Rdy -> C:\Data\Batch002 (Ready for upload)
2024-01-15 10:31:00 [INFO]   [3] batch003.Rdy -> C:\Data\Batch003 (Ready for upload)
2024-01-15 10:31:00 [INFO]   [4] batch004.Rdy -> C:\Data\Batch004 (Ready for upload)
2024-01-15 10:31:00 [INFO]   [5] batch005.Rdy -> C:\Data\Batch005 (Ready for upload)
2024-01-15 10:31:00 [INFO] QUEUE SUMMARY: 5 ready, 0 waiting, 3 consecutive failures
2024-01-15 10:31:00 [WARN] QUEUE STATUS: Processing is PAUSED due to consecutive failures
2024-01-15 10:31:00 [INFO] QUEUE STATUS: 2 trigger file(s) currently locked
```

## Database Integration

### New Event Type
- **QUEUE_STATUS**: Periodic queue status logging with detailed metrics

### Database Schema Enhancement
```sql
-- Service Events table now includes QUEUE_STATUS events
INSERT INTO ServiceEvents (EventTime, EventType, Description, Details, MachineName, ServiceName)
VALUES (
    GETDATE(), 
    'QUEUE_STATUS', 
    'Queue status: 3 pending, 1 ready, 2 waiting',
    'ConsecutiveFailures: 0, ProcessingPaused: False, DeletedTracking: 0, LockedFiles: 1'
);
```

### Monitoring Queries

#### Recent Queue Status
```sql
-- Query queue status events for monitoring
SELECT EventTime, Description, Details
FROM ServiceEvents 
WHERE EventType = 'QUEUE_STATUS'
ORDER BY EventTime DESC;
```

#### Queue Trends Analysis
```sql
-- Monitor queue trends over time
SELECT 
    DATEPART(HOUR, EventTime) AS Hour,
    AVG(CAST(SUBSTRING(Description, CHARINDEX(':', Description) + 2, CHARINDEX(' pending', Description) - CHARINDEX(':', Description) - 2) AS INT)) AS AvgQueueSize
FROM ServiceEvents 
WHERE EventType = 'QUEUE_STATUS'
  AND EventTime >= DATEADD(DAY, -1, GETDATE())
GROUP BY DATEPART(HOUR, EventTime)
ORDER BY Hour;
```

#### Processing Issues Detection
```sql
-- Check for processing issues (paused or high failure counts)
SELECT EventTime, Description, Details
FROM ServiceEvents 
WHERE EventType = 'QUEUE_STATUS'
  AND (Details LIKE '%ProcessingPaused: True%' OR Details LIKE '%ConsecutiveFailures: [5-9]%' OR Details LIKE '%ConsecutiveFailures: [1-9][0-9]%')
ORDER BY EventTime DESC;
```

## Operational Benefits

### 1. Real-Time Monitoring
- **Queue Visibility**: See exactly what's pending and when it will be processed
- **System Health**: Monitor for processing issues and failures
- **Performance Tracking**: Understand queue throughput and bottlenecks

### 2. Troubleshooting Support
- **Issue Detection**: Quickly identify when processing is paused or failing
- **Queue Analysis**: See which items are stuck and why
- **Timing Information**: Understand wait times and processing delays

### 3. Capacity Planning
- **Load Patterns**: Analyze queue size trends over time
- **Peak Usage**: Identify busy periods and resource requirements
- **Performance Metrics**: Track processing efficiency and throughput

### 4. Operational Awareness
- **Status at a Glance**: Regular updates on system state
- **Proactive Monitoring**: Early warning of potential issues
- **Historical Analysis**: Database records for trend analysis

## Configuration Best Practices

### Production Settings
```xml
<!-- Enable for operational visibility -->
<add key="EnableQueueStatusLogging" value="true" />

<!-- 30 seconds provides good balance of visibility vs log volume -->
<add key="QueueStatusIntervalSeconds" value="30" />
```

### Development/Testing Settings
```xml
<!-- May use shorter intervals for testing -->
<add key="EnableQueueStatusLogging" value="true" />
<add key="QueueStatusIntervalSeconds" value="10" />
```

### High-Volume Environments
```xml
<!-- May use longer intervals to reduce log volume -->
<add key="EnableQueueStatusLogging" value="true" />
<add key="QueueStatusIntervalSeconds" value="60" />
```

## Performance Considerations

### Log Volume Impact
- **30-second interval**: ~2,880 log entries per day
- **Database storage**: Minimal impact with structured logging
- **Log file size**: Manageable with proper log rotation

### Processing Overhead
- **Minimal CPU usage**: Simple queue enumeration and logging
- **Non-blocking**: Asynchronous database logging
- **Error isolation**: Queue status failures don't affect uploads

### Memory Usage
- **Snapshot approach**: Creates temporary array of queue items
- **Limited display**: Shows only first 10 items to control output
- **Efficient enumeration**: Uses LINQ for status calculations

## Integration with Existing Features

### Works Seamlessly With
- ✅ **File Locking**: Reports locked file counts
- ✅ **Database Logging**: Stores queue status in database
- ✅ **Retry Logic**: Shows consecutive failure counts
- ✅ **Queue Management**: Reports deleted trigger file tracking
- ✅ **Processing Control**: Indicates when processing is paused

### Complements Other Monitoring
- **Service Events**: Adds to existing service start/stop logging
- **Upload Events**: Provides context for upload activity
- **File Events**: Shows relationship between queue and processing
- **Error Logging**: Helps correlate issues with queue state

## Files Modified

### Enhanced Files
- **FileUploaderService.cs**: Complete queue status logging implementation
- **App.config.example**: Queue status logging configuration options
- **README.md**: Updated feature documentation and configuration
- **DatabaseSchema.sql**: New QUEUE_STATUS event type and monitoring queries

### Key Enhancements
1. **Timer Management**: Added queueStatusTimer with proper lifecycle management
2. **Configuration Loading**: Added queue status logging settings
3. **Status Logging Method**: Comprehensive LogQueueStatus implementation
4. **Database Integration**: QUEUE_STATUS events logged to ServiceEvents table
5. **Service Integration**: Queue status logging integrated with service start/stop

## Summary

The queue status logging implementation provides comprehensive visibility into the upload queue state with:

- **Regular Status Updates**: Every 30 seconds (configurable)
- **Detailed Queue Information**: Individual items with wait times and status
- **System Health Monitoring**: Processing state, failures, and file locking
- **Database Integration**: Structured logging for analysis and alerting
- **Operational Benefits**: Improved monitoring, troubleshooting, and capacity planning

This enhancement significantly improves operational visibility and troubleshooting capabilities while maintaining minimal performance impact and seamless integration with existing features.
