using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.ServiceProcess;
using System.Configuration;
using WinSCP;
using System.Linq;
using System.Collections.Generic;
using System.Xml;
using System.Xml.Linq;
using log4net;
using log4net.Config;
using System.Timers;

namespace SftpFileUploaderService
{
    public class FileUploaderService : ServiceBase
    {
        // Logger
        private static readonly ILog logger = LogManager.GetLogger(typeof(FileUploaderService));

        // Configuration
        private string ftpHost;
        private string ftpUsername;
        private string ftpPassword;
        private string ftpRemotePath;
        private string watchFolder;
        private string fingerprint;
        private int maxConcurrentUploads;
        private TimeSpan waitTimeBeforeUpload;
        private string[] includeFilePatterns;
        private string[] excludeFilePatterns;
        private bool deleteTriggerFileAfterUpload;
        private bool preserveFolderStructure;
        private string xmlFolderPathElement;
        private string xmlFolderPathAttribute;
        private bool includeSubfolders;
        private int maxSubfolderDepth;
        private string[] excludeSubfolderPatterns;
        private bool preCreateDirectories;
        private bool createDirectoriesRecursively;
        private bool enablePolling;
        private int pollingIntervalSeconds;
        private bool enableDetailedLogging;
        private int maxRetryAttempts;
        private int retryDelaySeconds;
        private bool enableRetryExponentialBackoff;
        private bool stopProcessingOnRetryExhaustion;
        private bool stopServiceOnCriticalFailure;
        private int maxConsecutiveFailures;

        // Service components
        private FileSystemWatcher watcher;
        private readonly object directoryCreationLock = new object();
        private ConcurrentQueue<FolderUploadInfo> uploadQueue;
        private SemaphoreSlim throttler;
        private CancellationTokenSource cancellationTokenSource;
        private Timer pollingTimer;
        private readonly HashSet<string> processedFiles = new HashSet<string>();
        private readonly object processedFilesLock = new object();
        private int consecutiveFailureCount = 0;
        private readonly object failureCountLock = new object();
        private volatile bool processingPaused = false;

        public FileUploaderService()
        {
            ServiceName = "SftpFileUploaderService";
            CanStop = true;
            CanPauseAndContinue = false;
            AutoLog = true;

            // Initialize log4net from app.config
            XmlConfigurator.Configure();
        }

        protected override void OnStart(string[] args)
        {
            try
            {
                // Load configuration from App.config
                LoadConfiguration();

                // Validate watch folder
                if (!ValidateWatchFolder())
                {
                    LogError("Service startup failed due to invalid watch folder configuration");
                    throw new InvalidOperationException("Invalid watch folder configuration");
                }

                // Initialize components
                uploadQueue = new ConcurrentQueue<FolderUploadInfo>();
                throttler = new SemaphoreSlim(maxConcurrentUploads);
                cancellationTokenSource = new CancellationTokenSource();

                // Set up file system watcher with enhanced configuration
                SetupFileSystemWatcher();

                // Set up polling if enabled
                if (enablePolling)
                {
                    SetupPollingTimer();
                }
            }
            catch (Exception ex)
            {
                LogError($"Error starting service: {ex.Message}", ex);
                throw;
            }

            // Start background processing
            Task.Run(() => ProcessQueueContinuouslyAsync(cancellationTokenSource.Token));

            LogInfo($"Service started successfully. Monitoring folder: {watchFolder} for *.Rdy trigger files");
            LogInfo($"File detection methods: FileSystemWatcher={true}, Polling={enablePolling}");
            LogInfo($"Configuration: MaxConcurrentUploads={maxConcurrentUploads}, WaitTime={waitTimeBeforeUpload.TotalMinutes} minutes");
            LogInfo($"Retry configuration: MaxRetries={maxRetryAttempts}, DelaySeconds={retryDelaySeconds}, ExponentialBackoff={enableRetryExponentialBackoff}");
            LogInfo($"Failure handling: StopProcessing={stopProcessingOnRetryExhaustion}, StopService={stopServiceOnCriticalFailure}, MaxConsecutiveFailures={maxConsecutiveFailures}");
        }

        protected override void OnStop()
        {
            LogInfo("Service stopping...");

            try
            {
                // Signal cancellation and cleanup
                cancellationTokenSource?.Cancel();

                // Stop polling timer
                pollingTimer?.Stop();
                pollingTimer?.Dispose();

                // Stop file system watcher
                if (watcher != null)
                {
                    watcher.EnableRaisingEvents = false;
                    watcher.Dispose();
                }

                // Cleanup other resources
                throttler?.Dispose();
                cancellationTokenSource?.Dispose();

                LogInfo("Service stopped successfully");
            }
            catch (Exception ex)
            {
                LogError($"Error stopping service: {ex.Message}", ex);
            }
        }

        private void LoadConfiguration()
        {
            ftpHost = ConfigurationManager.AppSettings["FtpHost"] ?? "sftp.example.com";
            ftpUsername = ConfigurationManager.AppSettings["FtpUsername"] ?? "username";
            ftpPassword = ConfigurationManager.AppSettings["FtpPassword"] ?? "password";
            ftpRemotePath = ConfigurationManager.AppSettings["FtpRemotePath"] ?? "/upload/";
            watchFolder = ConfigurationManager.AppSettings["WatchFolder"] ?? @"C:\path\to\watch\folder";
            fingerprint = ConfigurationManager.AppSettings["SshFingerprint"] ?? "ssh-rsa 2048 xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx";

            if (!int.TryParse(ConfigurationManager.AppSettings["MaxConcurrentUploads"], out maxConcurrentUploads))
                maxConcurrentUploads = 3;

            int waitMinutes;
            if (!int.TryParse(ConfigurationManager.AppSettings["WaitMinutesBeforeUpload"], out waitMinutes))
                waitMinutes = 20;

            waitTimeBeforeUpload = TimeSpan.FromMinutes(waitMinutes);

            // New configuration options for folder upload
            string includePatterns = ConfigurationManager.AppSettings["IncludeFilePatterns"] ?? "*.*";
            includeFilePatterns = includePatterns.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                .Select(p => p.Trim()).ToArray();

            string excludePatterns = ConfigurationManager.AppSettings["ExcludeFilePatterns"] ?? "*.Rdy;*.tmp;*.log";
            excludeFilePatterns = excludePatterns.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                .Select(p => p.Trim()).ToArray();

            if (!bool.TryParse(ConfigurationManager.AppSettings["DeleteTriggerFileAfterUpload"], out deleteTriggerFileAfterUpload))
                deleteTriggerFileAfterUpload = true;

            if (!bool.TryParse(ConfigurationManager.AppSettings["PreserveFolderStructure"], out preserveFolderStructure))
                preserveFolderStructure = false;

            // XML parsing configuration
            xmlFolderPathElement = ConfigurationManager.AppSettings["XmlFolderPathElement"] ?? "FolderPath";
            xmlFolderPathAttribute = ConfigurationManager.AppSettings["XmlFolderPathAttribute"] ?? "";

            // Subfolder handling configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["IncludeSubfolders"], out includeSubfolders))
                includeSubfolders = true; // Default to including subfolders

            if (!int.TryParse(ConfigurationManager.AppSettings["MaxSubfolderDepth"], out maxSubfolderDepth))
                maxSubfolderDepth = -1; // Default to unlimited depth

            string excludeSubfolders = ConfigurationManager.AppSettings["ExcludeSubfolderPatterns"] ?? "";
            excludeSubfolderPatterns = excludeSubfolders.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                       .Select(p => p.Trim()).ToArray();

            // Directory creation configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["PreCreateDirectories"], out preCreateDirectories))
                preCreateDirectories = true; // Default to pre-creating directories

            if (!bool.TryParse(ConfigurationManager.AppSettings["CreateDirectoriesRecursively"], out createDirectoriesRecursively))
                createDirectoriesRecursively = true; // Default to recursive creation

            // File detection configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["EnablePolling"], out enablePolling))
                enablePolling = false; // Default to FileSystemWatcher only

            if (!int.TryParse(ConfigurationManager.AppSettings["PollingIntervalSeconds"], out pollingIntervalSeconds))
                pollingIntervalSeconds = 30; // Default to 30 seconds

            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableDetailedLogging"], out enableDetailedLogging))
                enableDetailedLogging = false; // Default to normal logging

            // Retry configuration
            if (!int.TryParse(ConfigurationManager.AppSettings["MaxRetryAttempts"], out maxRetryAttempts))
                maxRetryAttempts = 3; // Default to 3 retry attempts

            if (!int.TryParse(ConfigurationManager.AppSettings["RetryDelaySeconds"], out retryDelaySeconds))
                retryDelaySeconds = 5; // Default to 5 seconds initial delay

            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableRetryExponentialBackoff"], out enableRetryExponentialBackoff))
                enableRetryExponentialBackoff = true; // Default to exponential backoff

            // Failure handling configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["StopProcessingOnRetryExhaustion"], out stopProcessingOnRetryExhaustion))
                stopProcessingOnRetryExhaustion = false; // Default to continue processing

            if (!bool.TryParse(ConfigurationManager.AppSettings["StopServiceOnCriticalFailure"], out stopServiceOnCriticalFailure))
                stopServiceOnCriticalFailure = false; // Default to continue service

            if (!int.TryParse(ConfigurationManager.AppSettings["MaxConsecutiveFailures"], out maxConsecutiveFailures))
                maxConsecutiveFailures = 5; // Default to 5 consecutive failures before pausing
        }

        private bool ValidateWatchFolder()
        {
            try
            {
                if (string.IsNullOrEmpty(watchFolder))
                {
                    LogError("Watch folder is not configured");
                    return false;
                }

                if (!Directory.Exists(watchFolder))
                {
                    LogError($"Watch folder does not exist: {watchFolder}");
                    return false;
                }

                // Test write permissions
                string testFile = Path.Combine(watchFolder, $"test_{Guid.NewGuid()}.tmp");
                try
                {
                    File.WriteAllText(testFile, "test");
                    File.Delete(testFile);
                    LogInfo($"Watch folder validation successful: {watchFolder}");
                }
                catch (Exception ex)
                {
                    LogWarn($"Watch folder may have permission issues: {ex.Message}");
                    // Don't fail validation for permission issues, just warn
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError($"Error validating watch folder: {ex.Message}", ex);
                return false;
            }
        }

        private void SetupFileSystemWatcher()
        {
            try
            {
                watcher = new FileSystemWatcher(watchFolder)
                {
                    Filter = "*.Rdy",
                    NotifyFilter = NotifyFilters.FileName | NotifyFilters.LastWrite | NotifyFilters.CreationTime,
                    IncludeSubdirectories = false,
                    InternalBufferSize = 8192 * 4, // Increase buffer size
                    EnableRaisingEvents = false // Will enable after setting up events
                };

                // Set up event handlers
                watcher.Created += OnFileCreated;
                watcher.Changed += OnFileChanged;
                watcher.Error += OnWatcherError;

                // Enable events
                watcher.EnableRaisingEvents = true;

                LogInfo($"FileSystemWatcher configured for: {watchFolder}");
                if (enableDetailedLogging)
                {
                    LogDebug($"Watcher settings - Filter: {watcher.Filter}, NotifyFilter: {watcher.NotifyFilter}, BufferSize: {watcher.InternalBufferSize}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error setting up FileSystemWatcher: {ex.Message}", ex);
                throw;
            }
        }

        private void SetupPollingTimer()
        {
            try
            {
                pollingTimer = new Timer(pollingIntervalSeconds * 1000);
                pollingTimer.Elapsed += OnPollingTimerElapsed;
                pollingTimer.AutoReset = true;
                pollingTimer.Start();

                LogInfo($"Polling timer configured with {pollingIntervalSeconds} second interval");
            }
            catch (Exception ex)
            {
                LogError($"Error setting up polling timer: {ex.Message}", ex);
                throw;
            }
        }

        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, CancellationToken cancellationToken = default)
        {
            Exception lastException = null;

            for (int attempt = 1; attempt <= maxRetryAttempts + 1; attempt++) // +1 for initial attempt
            {
                try
                {
                    if (attempt > 1)
                    {
                        LogInfo($"Retry attempt {attempt - 1}/{maxRetryAttempts} for {operationName}");
                    }

                    T result = await operation();

                    // Reset consecutive failure count on success
                    ResetConsecutiveFailureCount();

                    return result;
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    if (attempt <= maxRetryAttempts)
                    {
                        int delaySeconds = enableRetryExponentialBackoff
                            ? retryDelaySeconds * (int)Math.Pow(2, attempt - 1)
                            : retryDelaySeconds;

                        LogWarn($"Attempt {attempt} failed for {operationName}: {ex.Message}. Retrying in {delaySeconds} seconds...");

                        await Task.Delay(delaySeconds * 1000, cancellationToken);
                    }
                    else
                    {
                        LogError($"All {maxRetryAttempts + 1} attempts failed for {operationName}: {ex.Message}", ex);

                        // Handle retry exhaustion
                        HandleRetryExhaustion(operationName, ex);
                    }
                }
            }

            throw lastException ?? new InvalidOperationException($"Operation {operationName} failed after {maxRetryAttempts + 1} attempts");
        }

        private async Task ExecuteWithRetryAsync(Func<Task> operation, string operationName, CancellationToken cancellationToken = default)
        {
            await ExecuteWithRetryAsync(async () =>
            {
                await operation();
                return true; // Return dummy value for void operations
            }, operationName, cancellationToken);
        }

        private void HandleRetryExhaustion(string operationName, Exception lastException)
        {
            // Increment consecutive failure count
            IncrementConsecutiveFailureCount();

            LogError($"CRITICAL: Retry exhaustion for {operationName}. Consecutive failures: {consecutiveFailureCount}/{maxConsecutiveFailures}");

            // Check if we should pause processing
            if (consecutiveFailureCount >= maxConsecutiveFailures)
            {
                if (stopProcessingOnRetryExhaustion)
                {
                    LogError($"CRITICAL: Maximum consecutive failures ({maxConsecutiveFailures}) reached. Pausing processing.");
                    processingPaused = true;
                }

                if (stopServiceOnCriticalFailure)
                {
                    LogError($"CRITICAL: Maximum consecutive failures reached. Stopping service as configured.");
                    Task.Run(() =>
                    {
                        Thread.Sleep(5000); // Give time for logging
                        Stop();
                    });
                }
            }
        }

        private void IncrementConsecutiveFailureCount()
        {
            lock (failureCountLock)
            {
                consecutiveFailureCount++;
                LogWarn($"Consecutive failure count increased to: {consecutiveFailureCount}");
            }
        }

        private void ResetConsecutiveFailureCount()
        {
            lock (failureCountLock)
            {
                if (consecutiveFailureCount > 0)
                {
                    LogInfo($"Consecutive failure count reset from {consecutiveFailureCount} to 0 after successful operation");
                    consecutiveFailureCount = 0;

                    // Resume processing if it was paused
                    if (processingPaused)
                    {
                        processingPaused = false;
                        LogInfo("Processing resumed after successful operation");
                    }
                }
            }
        }

        private async Task ProcessQueueContinuouslyAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if processing is paused due to consecutive failures
                    if (processingPaused)
                    {
                        LogWarn($"Processing is paused due to {consecutiveFailureCount} consecutive failures. Waiting for manual intervention or successful operation...");
                        await Task.Delay(30000, cancellationToken); // Wait 30 seconds before checking again
                        continue;
                    }

                    if (uploadQueue.TryPeek(out FolderUploadInfo folderInfo))
                    {
                        // Check if the wait time has elapsed
                        TimeSpan elapsed = DateTime.Now - folderInfo.DetectionTime;

                        if (elapsed >= waitTimeBeforeUpload)
                        {
                            // Remove from queue
                            if (uploadQueue.TryDequeue(out folderInfo))
                            {
                                await throttler.WaitAsync(cancellationToken);

                                // Start a new task to handle the folder upload
                                _ = Task.Run(async () =>
                                {
                                    try
                                    {
                                        LogInfo($"Wait time elapsed for folder {folderInfo.FolderPath}, starting upload");
                                        await ExecuteWithRetryAsync(
                                            () => UploadFolderAsync(folderInfo),
                                            $"folder upload for {folderInfo.FolderPath}",
                                            cancellationToken);

                                        LogInfo($"Successfully completed upload for folder {folderInfo.FolderPath}");
                                    }
                                    catch (Exception ex)
                                    {
                                        LogError($"CRITICAL FAILURE: Folder upload failed after all retry attempts: {folderInfo.FolderPath}", ex);
                                        LogError($"Error details: {ex.Message}");

                                        // If configured to stop processing on retry exhaustion, this folder will be the last processed
                                        if (stopProcessingOnRetryExhaustion && consecutiveFailureCount >= maxConsecutiveFailures)
                                        {
                                            LogError($"Processing will be paused due to consecutive failures. Manual intervention required.");
                                        }

                                        // If configured to stop service, it will happen in HandleRetryExhaustion
                                        if (stopServiceOnCriticalFailure && consecutiveFailureCount >= maxConsecutiveFailures)
                                        {
                                            LogError($"Service will be stopped due to critical failure threshold reached.");
                                        }
                                    }
                                    finally
                                    {
                                        throttler.Release();
                                    }
                                }, cancellationToken);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogError($"Error in queue processing: {ex.Message}", ex);
                }

                // Sleep for a short time before checking again
                await Task.Delay(5000, cancellationToken); // Check every 5 seconds
            }
        }

        private void OnFileCreated(object sender, FileSystemEventArgs e)
        {
            if (enableDetailedLogging)
            {
                LogDebug($"FileSystemWatcher.Created event: {e.FullPath}");
            }
            ProcessDetectedFile(e.FullPath, "FileSystemWatcher.Created");
        }

        private void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            if (enableDetailedLogging)
            {
                LogDebug($"FileSystemWatcher.Changed event: {e.FullPath}");
            }
            ProcessDetectedFile(e.FullPath, "FileSystemWatcher.Changed");
        }

        private void OnWatcherError(object sender, ErrorEventArgs e)
        {
            LogError($"FileSystemWatcher error: {e.GetException().Message}", e.GetException());

            // Try to restart the watcher
            try
            {
                LogInfo("Attempting to restart FileSystemWatcher...");
                watcher.EnableRaisingEvents = false;
                Thread.Sleep(1000);
                watcher.EnableRaisingEvents = true;
                LogInfo("FileSystemWatcher restarted successfully");
            }
            catch (Exception ex)
            {
                LogError($"Failed to restart FileSystemWatcher: {ex.Message}", ex);
            }
        }

        private void OnPollingTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                if (enableDetailedLogging)
                {
                    LogDebug("Polling timer elapsed - scanning for new files");
                }

                var rdyFiles = Directory.GetFiles(watchFolder, "*.Rdy", SearchOption.TopDirectoryOnly);

                foreach (var file in rdyFiles)
                {
                    ProcessDetectedFile(file, "Polling");
                }

                if (enableDetailedLogging)
                {
                    LogDebug($"Polling scan completed - found {rdyFiles.Length} .Rdy files");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error during polling scan: {ex.Message}", ex);
            }
        }

        private void ProcessDetectedFile(string filePath, string detectionMethod)
        {
            try
            {
                // Check if we've already processed this file recently
                lock (processedFilesLock)
                {
                    if (processedFiles.Contains(filePath))
                    {
                        if (enableDetailedLogging)
                        {
                            LogDebug($"File already processed recently: {filePath}");
                        }
                        return;
                    }
                    processedFiles.Add(filePath);
                }

                LogInfo($"Detected trigger file via {detectionMethod}: {filePath}. Will parse XML and upload after {waitTimeBeforeUpload.TotalMinutes} minutes");

                // Wait a moment to ensure file is fully written
                Thread.Sleep(500);

                // Parse the XML file to get the folder path
                string folderPath = ParseFolderPathFromXml(filePath);

                if (!string.IsNullOrEmpty(folderPath))
                {
                    // Queue folder for upload with creation timestamp
                    uploadQueue.Enqueue(new FolderUploadInfo
                    {
                        TriggerFilePath = filePath,
                        FolderPath = folderPath,
                        DetectionTime = DateTime.Now
                    });

                    LogInfo($"Parsed folder path from XML: {folderPath}");
                }
                else
                {
                    LogError($"Could not parse folder path from XML file: {filePath}");
                }

                // Clean up processed files list periodically
                if (processedFiles.Count > 1000)
                {
                    lock (processedFilesLock)
                    {
                        processedFiles.Clear();
                        LogDebug("Cleared processed files cache");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error processing detected file {filePath}: {ex.Message}", ex);
            }
        }

        private async Task UploadFolderAsync(FolderUploadInfo folderInfo)
        {
            LogInfo($"Starting folder upload for {folderInfo.FolderPath}");

            try
            {
                // Get all files in the folder that match our criteria
                var filesToUpload = GetFilesToUpload(folderInfo.FolderPath);

                if (filesToUpload.Count == 0)
                {
                    LogWarn($"No files found to upload in folder {folderInfo.FolderPath}");
                    return;
                }

                LogInfo($"Found {filesToUpload.Count} files to upload from folder {folderInfo.FolderPath}");

                // Create session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = ftpHost,
                    UserName = ftpUsername,
                    Password = ftpPassword,
                    SshHostKeyFingerprint = fingerprint
                };

                await ExecuteWithRetryAsync(async () =>
                {
                    using (Session session = new Session())
                    {
                        // Connect with retry
                        session.Open(sessionOptions);
                        LogInfo($"Successfully connected to SFTP server: {ftpHost}");

                        // Pre-create directory structure if configured and preserving folder structure
                        if (preCreateDirectories && preserveFolderStructure)
                        {
                            await PreCreateDirectoryStructureAsync(session, filesToUpload, folderInfo.FolderPath);
                        }

                        // Upload files with multithreading
                        await UploadFilesMultithreadedAsync(session, filesToUpload, folderInfo.FolderPath);
                    }
                }, $"SFTP connection and upload for {folderInfo.FolderPath}");

                LogInfo($"Successfully completed folder upload for {folderInfo.FolderPath}");

                // Delete trigger file if configured to do so
                if (deleteTriggerFileAfterUpload && File.Exists(folderInfo.TriggerFilePath))
                {
                    try
                    {
                        File.Delete(folderInfo.TriggerFilePath);
                        LogInfo($"Deleted trigger file: {folderInfo.TriggerFilePath}");
                    }
                    catch (Exception ex)
                    {
                        LogWarn($"Could not delete trigger file {folderInfo.TriggerFilePath}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in folder upload for {folderInfo.FolderPath}: {ex.Message}", ex);
                throw;
            }
        }

        private List<string> GetFilesToUpload(string folderPath)
        {
            var filesToUpload = new List<string>();

            try
            {
                // Get all files in the folder
                var allFiles = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories);

                foreach (var file in allFiles)
                {
                    string fileName = Path.GetFileName(file);

                    // Check if file matches include patterns
                    bool includeFile = includeFilePatterns.Any(pattern =>
                        MatchesPattern(fileName, pattern));

                    // Check if file matches exclude patterns
                    bool excludeFile = excludeFilePatterns.Any(pattern =>
                        MatchesPattern(fileName, pattern));

                    if (includeFile && !excludeFile)
                    {
                        filesToUpload.Add(file);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error scanning folder {folderPath}: {ex.Message}", ex);
            }

            return filesToUpload;
        }

        private bool MatchesPattern(string fileName, string pattern)
        {
            // Simple wildcard matching - convert to regex
            string regexPattern = "^" + pattern.Replace("*", ".*").Replace("?", ".") + "$";
            return System.Text.RegularExpressions.Regex.IsMatch(fileName, regexPattern,
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }

        private async Task PreCreateDirectoryStructureAsync(Session session, List<string> filesToUpload, string baseFolderPath)
        {
            try
            {
                LogInfo("Pre-creating remote directory structure...");

                // Get all unique directory paths that need to be created
                var uniqueDirectories = new HashSet<string>();

                foreach (var filePath in filesToUpload)
                {
                    string relativePath = Path.GetRelativePath(baseFolderPath, filePath);
                    string remoteFilePath = ftpRemotePath.TrimEnd('/') + "/" + relativePath.Replace('\\', '/');
                    string remoteDir = Path.GetDirectoryName(remoteFilePath)?.Replace('\\', '/');

                    if (!string.IsNullOrEmpty(remoteDir))
                    {
                        // Add all parent directories to the set
                        string currentDir = remoteDir;
                        string baseRemotePath = ftpRemotePath.TrimEnd('/');

                        while (!string.IsNullOrEmpty(currentDir) && currentDir != baseRemotePath)
                        {
                            uniqueDirectories.Add(currentDir);
                            currentDir = Path.GetDirectoryName(currentDir)?.Replace('\\', '/');
                        }
                    }
                }

                // Sort directories by depth (shortest first) to create parent directories before children
                var sortedDirectories = uniqueDirectories
                    .OrderBy(dir => dir.Count(c => c == '/'))
                    .ToList();

                LogInfo($"Creating {sortedDirectories.Count} remote directories...");

                // Create directories sequentially to avoid conflicts
                foreach (var directory in sortedDirectories)
                {
                    try
                    {
                        if (!session.FileExists(directory))
                        {
                            session.CreateDirectory(directory);
                            LogDebug($"Created remote directory: {directory}");
                        }
                        else
                        {
                            LogDebug($"Remote directory already exists: {directory}");
                        }
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("already exists") || ex.Message.Contains("File exists"))
                        {
                            LogDebug($"Directory already exists (caught exception): {directory}");
                        }
                        else
                        {
                            LogWarn($"Could not create directory {directory}: {ex.Message}");
                        }
                    }
                }

                LogInfo("Completed pre-creating remote directory structure");
            }
            catch (Exception ex)
            {
                LogError($"Error pre-creating directory structure: {ex.Message}", ex);
                // Don't throw - continue with upload, individual file uploads will create directories as needed
            }

            await Task.CompletedTask;
        }

        private async Task UploadFilesMultithreadedAsync(Session session, List<string> filesToUpload, string baseFolderPath)
        {
            // Create a semaphore to limit concurrent file uploads within this folder upload
            using (var fileUploadSemaphore = new SemaphoreSlim(maxConcurrentUploads, maxConcurrentUploads))
            {
                var uploadTasks = filesToUpload.Select(async filePath =>
                {
                    await fileUploadSemaphore.WaitAsync();
                    try
                    {
                        await ExecuteWithRetryAsync(
                            () => UploadSingleFileAsync(session, filePath, baseFolderPath),
                            $"file upload for {Path.GetFileName(filePath)}");
                    }
                    finally
                    {
                        fileUploadSemaphore.Release();
                    }
                });

                await Task.WhenAll(uploadTasks);
            }
        }

        private void EnsureRemoteDirectoryExists(Session session, string remotePath)
        {
            try
            {
                // Normalize the path
                remotePath = remotePath.Replace('\\', '/').TrimEnd('/');

                // Skip if it's the root remote path
                string baseRemotePath = ftpRemotePath.TrimEnd('/');
                if (string.IsNullOrEmpty(remotePath) || remotePath == baseRemotePath)
                {
                    return;
                }

                LogDebug($"Ensuring remote directory exists: {remotePath}");

                // Use lock to prevent race conditions when multiple threads create directories
                lock (directoryCreationLock)
                {
                    try
                    {
                        // Check if directory already exists
                        if (session.FileExists(remotePath))
                        {
                            LogDebug($"Remote directory already exists: {remotePath}");
                            return;
                        }

                        // Create parent directories recursively
                        string parentPath = Path.GetDirectoryName(remotePath)?.Replace('\\', '/');
                        if (!string.IsNullOrEmpty(parentPath) && parentPath != baseRemotePath)
                        {
                            EnsureRemoteDirectoryExists(session, parentPath);
                        }

                        // Create the directory
                        session.CreateDirectory(remotePath);
                        LogInfo($"Created remote directory: {remotePath}");
                    }
                    catch (Exception ex)
                    {
                        // Check if the error is because directory already exists
                        if (ex.Message.Contains("already exists") || ex.Message.Contains("File exists"))
                        {
                            LogDebug($"Remote directory already exists (caught exception): {remotePath}");
                        }
                        else
                        {
                            LogError($"Error creating remote directory {remotePath}: {ex.Message}", ex);
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to ensure remote directory exists {remotePath}: {ex.Message}", ex);
                throw;
            }
        }

        private async Task UploadSingleFileAsync(Session session, string filePath, string baseFolderPath)
        {
            try
            {
                LogDebug($"Uploading file: {filePath}");

                string remotePath;
                if (preserveFolderStructure)
                {
                    // Preserve the folder structure relative to the base folder
                    string relativePath = Path.GetRelativePath(baseFolderPath, filePath);
                    remotePath = ftpRemotePath.TrimEnd('/') + "/" + relativePath.Replace('\\', '/');
                }
                else
                {
                    // Upload all files to the same remote directory
                    string fileName = Path.GetFileName(filePath);
                    remotePath = ftpRemotePath.TrimEnd('/') + "/" + fileName;
                }

                // Ensure remote directory exists
                string remoteDir = Path.GetDirectoryName(remotePath)?.Replace('\\', '/');
                if (!string.IsNullOrEmpty(remoteDir))
                {
                    EnsureRemoteDirectoryExists(session, remoteDir);
                }

                // Upload the file
                TransferOptions transferOptions = new TransferOptions
                {
                    TransferMode = TransferMode.Binary
                };

                TransferOperationResult result = session.PutFiles(filePath, remotePath, false, transferOptions);
                result.Check();

                LogInfo($"Successfully uploaded: {filePath} -> {remotePath}");
            }
            catch (Exception ex)
            {
                LogError($"Error uploading file {filePath}: {ex.Message}", ex);
                throw;
            }

            await Task.CompletedTask;
        }

        private string ParseFolderPathFromXml(string xmlFilePath)
        {
            try
            {
                LogDebug($"Parsing XML file: {xmlFilePath}");

                // Wait a moment to ensure file is fully written
                Thread.Sleep(1000);

                // Load the XML document
                XDocument doc = XDocument.Load(xmlFilePath);

                string folderPath = null;

                // Try to find the folder path using the configured element name
                if (!string.IsNullOrEmpty(xmlFolderPathAttribute))
                {
                    // Look for an attribute with the specified name
                    var elementWithAttribute = doc.Descendants()
                        .FirstOrDefault(e => e.Attribute(xmlFolderPathAttribute) != null);

                    if (elementWithAttribute != null)
                    {
                        folderPath = elementWithAttribute.Attribute(xmlFolderPathAttribute).Value;
                    }
                }
                else
                {
                    // Look for an element with the specified name
                    var element = doc.Descendants(xmlFolderPathElement).FirstOrDefault();
                    if (element != null)
                    {
                        folderPath = element.Value;
                    }
                }

                // If not found with configured names, try common element names
                if (string.IsNullOrEmpty(folderPath))
                {
                    var commonElementNames = new[] { "FolderPath", "Path", "Directory", "Folder", "SourcePath", "UploadPath" };

                    foreach (var elementName in commonElementNames)
                    {
                        var element = doc.Descendants(elementName).FirstOrDefault();
                        if (element != null && !string.IsNullOrEmpty(element.Value))
                        {
                            folderPath = element.Value;
                            LogDebug($"Found folder path using element '{elementName}': {folderPath}");
                            break;
                        }
                    }
                }

                // If still not found, try common attribute names
                if (string.IsNullOrEmpty(folderPath))
                {
                    var commonAttributeNames = new[] { "path", "folder", "directory", "source", "upload" };

                    foreach (var attrName in commonAttributeNames)
                    {
                        var elementWithAttr = doc.Descendants()
                            .FirstOrDefault(e => e.Attribute(attrName) != null);

                        if (elementWithAttr != null)
                        {
                            folderPath = elementWithAttr.Attribute(attrName).Value;
                            LogDebug($"Found folder path using attribute '{attrName}': {folderPath}");
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(folderPath))
                {
                    // Validate that the folder exists
                    if (Directory.Exists(folderPath))
                    {
                        WriteToEventLog($"Successfully parsed and validated folder path: {folderPath}");
                        return folderPath;
                    }
                    else
                    {
                        WriteToEventLog($"Parsed folder path does not exist: {folderPath}", true);
                    }
                }
                else
                {
                    WriteToEventLog($"Could not find folder path in XML file. XML content: {doc.ToString()}", true);
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"Error parsing XML file {xmlFilePath}: {ex.Message}", true);
            }

            return null;
        }

        private void LogInfo(string message)
        {
            logger.Info(message);
        }

        private void LogError(string message, Exception ex = null)
        {
            if (ex != null)
                logger.Error(message, ex);
            else
                logger.Error(message);
        }

        private void LogWarn(string message)
        {
            logger.Warn(message);
        }

        private void LogDebug(string message)
        {
            logger.Debug(message);
        }

        // Class to store folder upload information with detection time
        private class FolderUploadInfo
        {
            public string TriggerFilePath { get; set; }
            public string FolderPath { get; set; }
            public DateTime DetectionTime { get; set; }
        }
    }
}