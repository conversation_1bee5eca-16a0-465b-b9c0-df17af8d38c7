using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.ServiceProcess;
using System.Configuration;
using Renci.SshNet;
using System.Linq;
using System.Collections.Generic;
using System.Xml;
using System.Xml.Linq;
using log4net;
using log4net.Config;
using System.Timers;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;

namespace SftpFileUploaderService
{
    public class FileUploaderService : ServiceBase
    {
        // Logger
        private static readonly ILog logger = LogManager.GetLogger(typeof(FileUploaderService));

        // Configuration
        private string ftpHost;
        private string ftpUsername;
        private string ftpPassword;
        private string ftpRemotePath;
        private string watchFolder;
        private string fingerprint;
        private int sftpPort;
        private int connectionTimeoutSeconds;
        private int operationTimeoutSeconds;
        private int maxConcurrentUploads;
        private TimeSpan waitTimeBeforeUpload;
        private string[] includeFilePatterns;
        private string[] excludeFilePatterns;
        private bool deleteTriggerFileAfterUpload;
        private bool preserveFolderStructure;
        private string xmlFolderPathElement;
        private string xmlFolderPathAttribute;
        private bool includeSubfolders;
        private int maxSubfolderDepth;
        private string[] excludeSubfolderPatterns;
        private bool preCreateDirectories;
        private bool createDirectoriesRecursively;
        private bool enablePolling;
        private int pollingIntervalSeconds;
        private bool enableDetailedLogging;
        private int maxRetryAttempts;
        private int retryDelaySeconds;
        private bool enableRetryExponentialBackoff;
        private bool stopProcessingOnRetryExhaustion;
        private bool stopServiceOnCriticalFailure;
        private int maxConsecutiveFailures;
        private bool removeFromQueueOnTriggerFileDeleted;

        // Database configuration
        private bool enableDatabaseLogging;
        private string databaseConnectionString;
        private int databaseTimeoutSeconds;
        private bool logServiceEvents;
        private bool logUploadEvents;
        private bool logFileEvents;

        // File locking configuration
        private bool enableTriggerFileLocking;
        private int fileLockTimeoutSeconds;
        private readonly ConcurrentDictionary<string, FileStream> lockedTriggerFiles = new ConcurrentDictionary<string, FileStream>();

        // Queue monitoring configuration
        private bool enableQueueStatusLogging;
        private int queueStatusIntervalSeconds;

        // Queue management configuration
        private bool enableImmediateDequeue;

        // Service components
        private FileSystemWatcher watcher;
        private readonly object directoryCreationLock = new object();
        private ConcurrentQueue<FolderUploadInfo> uploadQueue;
        private SemaphoreSlim throttler;
        private CancellationTokenSource cancellationTokenSource;
        private Timer pollingTimer;
        private Timer queueStatusTimer;
        private readonly HashSet<string> processedFiles = new HashSet<string>();
        private readonly object processedFilesLock = new object();
        private int consecutiveFailureCount = 0;
        private readonly object failureCountLock = new object();
        private volatile bool processingPaused = false;
        private readonly HashSet<string> deletedTriggerFiles = new HashSet<string>();
        private readonly object deletedTriggerFilesLock = new object();

        public FileUploaderService()
        {
            ServiceName = "SftpFileUploaderService";
            CanStop = true;
            CanPauseAndContinue = false;
            AutoLog = true;

            // Initialize log4net from app.config
            XmlConfigurator.Configure();
        }

        protected override void OnStart(string[] args)
        {
            try
            {
                // Load configuration from App.config
                LoadConfiguration();

                // Validate watch folder
                if (!ValidateWatchFolder())
                {
                    LogError("Service startup failed due to invalid watch folder configuration");
                    throw new InvalidOperationException("Invalid watch folder configuration");
                }

                // Initialize components
                uploadQueue = new ConcurrentQueue<FolderUploadInfo>();
                throttler = new SemaphoreSlim(maxConcurrentUploads);
                cancellationTokenSource = new CancellationTokenSource();

                // Set up file system watcher with enhanced configuration
                SetupFileSystemWatcher();

                // Set up polling if enabled
                if (enablePolling)
                {
                    SetupPollingTimer();
                }

                // Set up queue status logging if enabled
                if (enableQueueStatusLogging)
                {
                    SetupQueueStatusTimer();
                }
            }
            catch (Exception ex)
            {
                LogError($"Error starting service: {ex.Message}", ex);
                throw;
            }

            // Start background processing
            Task.Run(() => ProcessQueueContinuouslyAsync(cancellationTokenSource.Token));

            LogInfo($"Service started successfully. Monitoring folder: {watchFolder} for *.Rdy trigger files");
            LogInfo($"File detection methods: FileSystemWatcher={true}, Polling={enablePolling}");
            LogInfo($"Configuration: MaxConcurrentUploads={maxConcurrentUploads}, WaitTime={waitTimeBeforeUpload.TotalMinutes} minutes");
            LogInfo($"Retry configuration: MaxRetries={maxRetryAttempts}, DelaySeconds={retryDelaySeconds}, ExponentialBackoff={enableRetryExponentialBackoff}");
            LogInfo($"Failure handling: StopProcessing={stopProcessingOnRetryExhaustion}, StopService={stopServiceOnCriticalFailure}, MaxConsecutiveFailures={maxConsecutiveFailures}");
            LogInfo($"Queue management: RemoveOnTriggerFileDeleted={removeFromQueueOnTriggerFileDeleted}, ImmediateDequeue={enableImmediateDequeue}, QueueStatusLogging={enableQueueStatusLogging}");
            LogInfo($"Database logging: Enabled={enableDatabaseLogging}, ServiceEvents={logServiceEvents}, UploadEvents={logUploadEvents}, FileEvents={logFileEvents}");
            LogInfo($"File locking: Enabled={enableTriggerFileLocking}, TimeoutSeconds={fileLockTimeoutSeconds}");

            if (enableQueueStatusLogging)
            {
                LogInfo($"Queue status logging: Enabled, Interval={queueStatusIntervalSeconds} seconds");
            }

            // Log service start to database
            _ = Task.Run(async () =>
            {
                try
                {
                    await LogServiceEventAsync("SERVICE_START", "File uploader service started successfully",
                        $"WatchFolder: {watchFolder}, MaxConcurrentUploads: {maxConcurrentUploads}");
                }
                catch (Exception ex)
                {
                    LogError($"Failed to log service start to database: {ex.Message}", ex);
                }
            });
        }

        protected override void OnStop()
        {
            LogInfo("Service stopping...");

            try
            {
                // Log service stop to database (do this first while service is still running)
                try
                {
                    var stopTask = LogServiceEventAsync("SERVICE_STOP", "File uploader service stopping",
                        $"Uptime: {DateTime.Now - Process.GetCurrentProcess().StartTime:dd\\.hh\\:mm\\:ss}");
                    stopTask.Wait(TimeSpan.FromSeconds(5)); // Wait up to 5 seconds for database logging
                }
                catch (Exception ex)
                {
                    LogError($"Failed to log service stop to database: {ex.Message}", ex);
                }

                // Signal cancellation and cleanup
                cancellationTokenSource?.Cancel();

                // Stop polling timer
                pollingTimer?.Stop();
                pollingTimer?.Dispose();

                // Stop queue status timer
                queueStatusTimer?.Stop();
                queueStatusTimer?.Dispose();

                // Stop file system watcher
                if (watcher != null)
                {
                    watcher.EnableRaisingEvents = false;
                    watcher.Dispose();
                }

                // Release all trigger file locks
                ReleaseAllTriggerFileLocks();

                // Cleanup other resources
                throttler?.Dispose();
                cancellationTokenSource?.Dispose();

                LogInfo("Service stopped successfully");
            }
            catch (Exception ex)
            {
                LogError($"Error stopping service: {ex.Message}", ex);
            }
        }

        private void LoadConfiguration()
        {
            ftpHost = ConfigurationManager.AppSettings["FtpHost"] ?? "sftp.example.com";
            ftpUsername = ConfigurationManager.AppSettings["FtpUsername"] ?? "username";
            ftpPassword = ConfigurationManager.AppSettings["FtpPassword"] ?? "password";
            ftpRemotePath = ConfigurationManager.AppSettings["FtpRemotePath"] ?? "/upload/";
            watchFolder = ConfigurationManager.AppSettings["WatchFolder"] ?? @"C:\path\to\watch\folder";
            fingerprint = ConfigurationManager.AppSettings["SshFingerprint"] ?? "ssh-rsa 2048 xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx";

            // SSH.Net specific configuration
            if (!int.TryParse(ConfigurationManager.AppSettings["SftpPort"], out sftpPort))
                sftpPort = 22; // Default SFTP port

            if (!int.TryParse(ConfigurationManager.AppSettings["ConnectionTimeoutSeconds"], out connectionTimeoutSeconds))
                connectionTimeoutSeconds = 30; // Default connection timeout

            if (!int.TryParse(ConfigurationManager.AppSettings["OperationTimeoutSeconds"], out operationTimeoutSeconds))
                operationTimeoutSeconds = 60; // Default operation timeout

            if (!int.TryParse(ConfigurationManager.AppSettings["MaxConcurrentUploads"], out maxConcurrentUploads))
                maxConcurrentUploads = 3;

            int waitMinutes;
            if (!int.TryParse(ConfigurationManager.AppSettings["WaitMinutesBeforeUpload"], out waitMinutes))
                waitMinutes = 20;

            waitTimeBeforeUpload = TimeSpan.FromMinutes(waitMinutes);

            // New configuration options for folder upload
            string includePatterns = ConfigurationManager.AppSettings["IncludeFilePatterns"] ?? "*.*";
            includeFilePatterns = includePatterns.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                .Select(p => p.Trim()).ToArray();

            string excludePatterns = ConfigurationManager.AppSettings["ExcludeFilePatterns"] ?? "*.Rdy;*.tmp;*.log";
            excludeFilePatterns = excludePatterns.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                .Select(p => p.Trim()).ToArray();

            if (!bool.TryParse(ConfigurationManager.AppSettings["DeleteTriggerFileAfterUpload"], out deleteTriggerFileAfterUpload))
                deleteTriggerFileAfterUpload = true;

            if (!bool.TryParse(ConfigurationManager.AppSettings["PreserveFolderStructure"], out preserveFolderStructure))
                preserveFolderStructure = false;

            // XML parsing configuration
            xmlFolderPathElement = ConfigurationManager.AppSettings["XmlFolderPathElement"] ?? "FolderPath";
            xmlFolderPathAttribute = ConfigurationManager.AppSettings["XmlFolderPathAttribute"] ?? "";

            // Subfolder handling configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["IncludeSubfolders"], out includeSubfolders))
                includeSubfolders = true; // Default to including subfolders

            if (!int.TryParse(ConfigurationManager.AppSettings["MaxSubfolderDepth"], out maxSubfolderDepth))
                maxSubfolderDepth = -1; // Default to unlimited depth

            string excludeSubfolders = ConfigurationManager.AppSettings["ExcludeSubfolderPatterns"] ?? "";
            excludeSubfolderPatterns = excludeSubfolders.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                       .Select(p => p.Trim()).ToArray();

            // Directory creation configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["PreCreateDirectories"], out preCreateDirectories))
                preCreateDirectories = true; // Default to pre-creating directories

            if (!bool.TryParse(ConfigurationManager.AppSettings["CreateDirectoriesRecursively"], out createDirectoriesRecursively))
                createDirectoriesRecursively = true; // Default to recursive creation

            // File detection configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["EnablePolling"], out enablePolling))
                enablePolling = false; // Default to FileSystemWatcher only

            if (!int.TryParse(ConfigurationManager.AppSettings["PollingIntervalSeconds"], out pollingIntervalSeconds))
                pollingIntervalSeconds = 30; // Default to 30 seconds

            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableDetailedLogging"], out enableDetailedLogging))
                enableDetailedLogging = false; // Default to normal logging

            // Retry configuration
            if (!int.TryParse(ConfigurationManager.AppSettings["MaxRetryAttempts"], out maxRetryAttempts))
                maxRetryAttempts = 3; // Default to 3 retry attempts

            if (!int.TryParse(ConfigurationManager.AppSettings["RetryDelaySeconds"], out retryDelaySeconds))
                retryDelaySeconds = 5; // Default to 5 seconds initial delay

            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableRetryExponentialBackoff"], out enableRetryExponentialBackoff))
                enableRetryExponentialBackoff = true; // Default to exponential backoff

            // Failure handling configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["StopProcessingOnRetryExhaustion"], out stopProcessingOnRetryExhaustion))
                stopProcessingOnRetryExhaustion = false; // Default to continue processing

            if (!bool.TryParse(ConfigurationManager.AppSettings["StopServiceOnCriticalFailure"], out stopServiceOnCriticalFailure))
                stopServiceOnCriticalFailure = false; // Default to continue service

            if (!int.TryParse(ConfigurationManager.AppSettings["MaxConsecutiveFailures"], out maxConsecutiveFailures))
                maxConsecutiveFailures = 5; // Default to 5 consecutive failures before pausing

            // Queue management configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["RemoveFromQueueOnTriggerFileDeleted"], out removeFromQueueOnTriggerFileDeleted))
                removeFromQueueOnTriggerFileDeleted = true; // Default to remove from queue when trigger file is deleted

            // Database configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableDatabaseLogging"], out enableDatabaseLogging))
                enableDatabaseLogging = false; // Default to disabled

            databaseConnectionString = ConfigurationManager.AppSettings["DatabaseConnectionString"] ?? "";

            if (!int.TryParse(ConfigurationManager.AppSettings["DatabaseTimeoutSeconds"], out databaseTimeoutSeconds))
                databaseTimeoutSeconds = 30; // Default to 30 seconds

            if (!bool.TryParse(ConfigurationManager.AppSettings["LogServiceEvents"], out logServiceEvents))
                logServiceEvents = true; // Default to log service start/stop

            if (!bool.TryParse(ConfigurationManager.AppSettings["LogUploadEvents"], out logUploadEvents))
                logUploadEvents = true; // Default to log upload start/end

            if (!bool.TryParse(ConfigurationManager.AppSettings["LogFileEvents"], out logFileEvents))
                logFileEvents = false; // Default to not log individual file events

            // File locking configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableTriggerFileLocking"], out enableTriggerFileLocking))
                enableTriggerFileLocking = true; // Default to enabled for data integrity

            if (!int.TryParse(ConfigurationManager.AppSettings["FileLockTimeoutSeconds"], out fileLockTimeoutSeconds))
                fileLockTimeoutSeconds = 30; // Default to 30 seconds timeout

            // Queue monitoring configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableQueueStatusLogging"], out enableQueueStatusLogging))
                enableQueueStatusLogging = true; // Default to enabled for monitoring

            if (!int.TryParse(ConfigurationManager.AppSettings["QueueStatusIntervalSeconds"], out queueStatusIntervalSeconds))
                queueStatusIntervalSeconds = 30; // Default to 30 seconds interval

            // Queue management configuration
            if (!bool.TryParse(ConfigurationManager.AppSettings["EnableImmediateDequeue"], out enableImmediateDequeue))
                enableImmediateDequeue = true; // Default to enabled for efficiency
        }

        private bool ValidateWatchFolder()
        {
            try
            {
                if (string.IsNullOrEmpty(watchFolder))
                {
                    LogError("Watch folder is not configured");
                    return false;
                }

                if (!Directory.Exists(watchFolder))
                {
                    LogError($"Watch folder does not exist: {watchFolder}");
                    return false;
                }

                // Test write permissions
                string testFile = Path.Combine(watchFolder, $"test_{Guid.NewGuid()}.tmp");
                try
                {
                    File.WriteAllText(testFile, "test");
                    File.Delete(testFile);
                    LogInfo($"Watch folder validation successful: {watchFolder}");
                }
                catch (Exception ex)
                {
                    LogWarn($"Watch folder may have permission issues: {ex.Message}");
                    // Don't fail validation for permission issues, just warn
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError($"Error validating watch folder: {ex.Message}", ex);
                return false;
            }
        }

        private void SetupFileSystemWatcher()
        {
            try
            {
                watcher = new FileSystemWatcher(watchFolder)
                {
                    Filter = "*.Rdy",
                    NotifyFilter = NotifyFilters.FileName | NotifyFilters.LastWrite | NotifyFilters.CreationTime,
                    IncludeSubdirectories = false,
                    InternalBufferSize = 8192 * 4, // Increase buffer size
                    EnableRaisingEvents = false // Will enable after setting up events
                };

                // Set up event handlers
                watcher.Created += OnFileCreated;
                watcher.Changed += OnFileChanged;
                watcher.Deleted += OnFileDeleted;
                watcher.Error += OnWatcherError;

                // Enable events
                watcher.EnableRaisingEvents = true;

                LogInfo($"FileSystemWatcher configured for: {watchFolder}");
                if (enableDetailedLogging)
                {
                    LogDebug($"Watcher settings - Filter: {watcher.Filter}, NotifyFilter: {watcher.NotifyFilter}, BufferSize: {watcher.InternalBufferSize}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error setting up FileSystemWatcher: {ex.Message}", ex);
                throw;
            }
        }

        private void SetupPollingTimer()
        {
            try
            {
                pollingTimer = new Timer(pollingIntervalSeconds * 1000);
                pollingTimer.Elapsed += OnPollingTimerElapsed;
                pollingTimer.AutoReset = true;
                pollingTimer.Start();

                LogInfo($"Polling timer configured with {pollingIntervalSeconds} second interval");
            }
            catch (Exception ex)
            {
                LogError($"Error setting up polling timer: {ex.Message}", ex);
                throw;
            }
        }

        private void SetupQueueStatusTimer()
        {
            try
            {
                queueStatusTimer = new Timer(queueStatusIntervalSeconds * 1000);
                queueStatusTimer.Elapsed += OnQueueStatusTimerElapsed;
                queueStatusTimer.AutoReset = true;
                queueStatusTimer.Start();

                LogInfo($"Queue status timer configured with {queueStatusIntervalSeconds} second interval");
            }
            catch (Exception ex)
            {
                LogError($"Error setting up queue status timer: {ex.Message}", ex);
                throw;
            }
        }

        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, CancellationToken cancellationToken = default)
        {
            Exception lastException = null;

            for (int attempt = 1; attempt <= maxRetryAttempts + 1; attempt++) // +1 for initial attempt
            {
                try
                {
                    if (attempt > 1)
                    {
                        LogInfo($"Retry attempt {attempt - 1}/{maxRetryAttempts} for {operationName}");
                    }

                    T result = await operation();

                    // Reset consecutive failure count on success
                    ResetConsecutiveFailureCount();

                    return result;
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    if (attempt <= maxRetryAttempts)
                    {
                        int delaySeconds = enableRetryExponentialBackoff
                            ? retryDelaySeconds * (int)Math.Pow(2, attempt - 1)
                            : retryDelaySeconds;

                        LogWarn($"Attempt {attempt} failed for {operationName}: {ex.Message}. Retrying in {delaySeconds} seconds...");

                        await Task.Delay(delaySeconds * 1000, cancellationToken);
                    }
                    else
                    {
                        LogError($"All {maxRetryAttempts + 1} attempts failed for {operationName}: {ex.Message}", ex);

                        // Handle retry exhaustion
                        HandleRetryExhaustion(operationName, ex);
                    }
                }
            }

            throw lastException ?? new InvalidOperationException($"Operation {operationName} failed after {maxRetryAttempts + 1} attempts");
        }

        private async Task ExecuteWithRetryAsync(Func<Task> operation, string operationName, CancellationToken cancellationToken = default)
        {
            await ExecuteWithRetryAsync(async () =>
            {
                await operation();
                return true; // Return dummy value for void operations
            }, operationName, cancellationToken);
        }

        private void HandleRetryExhaustion(string operationName, Exception lastException)
        {
            // Increment consecutive failure count
            IncrementConsecutiveFailureCount();

            LogError($"CRITICAL: Retry exhaustion for {operationName}. Consecutive failures: {consecutiveFailureCount}/{maxConsecutiveFailures}");

            // Check if we should pause processing
            if (consecutiveFailureCount >= maxConsecutiveFailures)
            {
                if (stopProcessingOnRetryExhaustion)
                {
                    LogError($"CRITICAL: Maximum consecutive failures ({maxConsecutiveFailures}) reached. Pausing processing.");
                    processingPaused = true;
                }

                if (stopServiceOnCriticalFailure)
                {
                    LogError($"CRITICAL: Maximum consecutive failures reached. Stopping service as configured.");
                    Task.Run(() =>
                    {
                        Thread.Sleep(5000); // Give time for logging
                        Stop();
                    });
                }
            }
        }

        private void IncrementConsecutiveFailureCount()
        {
            lock (failureCountLock)
            {
                consecutiveFailureCount++;
                LogWarn($"Consecutive failure count increased to: {consecutiveFailureCount}");
            }
        }

        private void ResetConsecutiveFailureCount()
        {
            lock (failureCountLock)
            {
                if (consecutiveFailureCount > 0)
                {
                    LogInfo($"Consecutive failure count reset from {consecutiveFailureCount} to 0 after successful operation");
                    consecutiveFailureCount = 0;

                    // Resume processing if it was paused
                    if (processingPaused)
                    {
                        processingPaused = false;
                        LogInfo("Processing resumed after successful operation");
                    }
                }
            }
        }

        private async Task ProcessQueueContinuouslyAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if processing is paused due to consecutive failures
                    if (processingPaused)
                    {
                        LogWarn($"Processing is paused due to {consecutiveFailureCount} consecutive failures. Waiting for manual intervention or successful operation...");
                        await Task.Delay(30000, cancellationToken); // Wait 30 seconds before checking again
                        continue;
                    }

                    if (uploadQueue.TryPeek(out FolderUploadInfo folderInfo))
                    {
                        // Check if the trigger file has been deleted
                        bool triggerFileDeleted = false;
                        lock (deletedTriggerFilesLock)
                        {
                            triggerFileDeleted = deletedTriggerFiles.Contains(folderInfo.TriggerFilePath);
                        }

                        if (triggerFileDeleted)
                        {
                            // Remove from queue and skip processing
                            if (uploadQueue.TryDequeue(out folderInfo))
                            {
                                LogInfo($"Skipping upload for {folderInfo.FolderPath} - trigger file was deleted: {folderInfo.TriggerFilePath}");
                            }
                            continue; // Check next item in queue
                        }

                        // Check if the wait time has elapsed
                        TimeSpan elapsed = DateTime.Now - folderInfo.DetectionTime;

                        if (elapsed >= waitTimeBeforeUpload)
                        {
                            // Remove from queue
                            if (uploadQueue.TryDequeue(out folderInfo))
                            {
                                await throttler.WaitAsync(cancellationToken);

                                // Start a new task to handle the folder upload
                                _ = Task.Run(async () =>
                                {
                                    try
                                    {
                                        LogInfo($"Wait time elapsed for folder {folderInfo.FolderPath}, starting upload");
                                        await ExecuteWithRetryAsync(
                                            () => UploadFolderAsync(folderInfo),
                                            $"folder upload for {folderInfo.FolderPath}",
                                            cancellationToken);

                                        LogInfo($"Successfully completed upload for folder {folderInfo.FolderPath}");
                                    }
                                    catch (Exception ex)
                                    {
                                        LogError($"CRITICAL FAILURE: Folder upload failed after all retry attempts: {folderInfo.FolderPath}", ex);
                                        LogError($"Error details: {ex.Message}");

                                        // If configured to stop processing on retry exhaustion, this folder will be the last processed
                                        if (stopProcessingOnRetryExhaustion && consecutiveFailureCount >= maxConsecutiveFailures)
                                        {
                                            LogError($"Processing will be paused due to consecutive failures. Manual intervention required.");
                                        }

                                        // If configured to stop service, it will happen in HandleRetryExhaustion
                                        if (stopServiceOnCriticalFailure && consecutiveFailureCount >= maxConsecutiveFailures)
                                        {
                                            LogError($"Service will be stopped due to critical failure threshold reached.");
                                        }
                                    }
                                    finally
                                    {
                                        throttler.Release();
                                    }
                                }, cancellationToken);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogError($"Error in queue processing: {ex.Message}", ex);
                }

                // Sleep for a short time before checking again
                await Task.Delay(5000, cancellationToken); // Check every 5 seconds
            }
        }

        private void OnFileCreated(object sender, FileSystemEventArgs e)
        {
            if (enableDetailedLogging)
            {
                LogDebug($"FileSystemWatcher.Created event: {e.FullPath}");
            }
            ProcessDetectedFile(e.FullPath, "FileSystemWatcher.Created");
        }

        private void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            if (enableDetailedLogging)
            {
                LogDebug($"FileSystemWatcher.Changed event: {e.FullPath}");
            }
            ProcessDetectedFile(e.FullPath, "FileSystemWatcher.Changed");
        }

        private void OnFileDeleted(object sender, FileSystemEventArgs e)
        {
            if (enableDetailedLogging)
            {
                LogDebug($"FileSystemWatcher.Deleted event: {e.FullPath}");
            }

            if (removeFromQueueOnTriggerFileDeleted)
            {
                ProcessTriggerFileDeleted(e.FullPath);
            }
        }

        private void ProcessTriggerFileDeleted(string deletedFilePath)
        {
            try
            {
                if (enableImmediateDequeue)
                {
                    LogInfo($"Trigger file deleted: {deletedFilePath}. Immediately removing from upload queue.");

                    // Immediately remove matching items from the queue
                    int removedCount = DequeueItemsForDeletedTriggerFile(deletedFilePath);

                    if (removedCount > 0)
                    {
                        LogInfo($"Immediately removed {removedCount} upload(s) from queue due to deleted trigger file: {Path.GetFileName(deletedFilePath)}");

                        // Log queue removal summary to database
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await LogServiceEventAsync("QUEUE_CLEANUP",
                                    $"Removed {removedCount} upload(s) from queue due to trigger file deletion",
                                    $"TriggerFile: {Path.GetFileName(deletedFilePath)}, RemovedCount: {removedCount}, Method: Immediate");
                            }
                            catch (Exception ex)
                            {
                                LogError($"Failed to log queue cleanup to database: {ex.Message}", ex);
                            }
                        });
                    }
                    else
                    {
                        if (enableDetailedLogging)
                        {
                            LogDebug($"No queue items found for deleted trigger file: {Path.GetFileName(deletedFilePath)}");
                        }
                    }
                }
                else
                {
                    LogInfo($"Trigger file deleted: {deletedFilePath}. Marking for removal during processing.");
                }

                // Add to deleted files tracking for any remaining references or deferred processing
                lock (deletedTriggerFilesLock)
                {
                    deletedTriggerFiles.Add(deletedFilePath);
                }

                // Clean up deleted files tracking periodically
                if (deletedTriggerFiles.Count > 100)
                {
                    lock (deletedTriggerFilesLock)
                    {
                        // Keep only recent deletions (this is a simple cleanup strategy)
                        if (deletedTriggerFiles.Count > 200)
                        {
                            deletedTriggerFiles.Clear();
                            LogDebug("Cleared deleted trigger files cache");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error processing deleted trigger file {deletedFilePath}: {ex.Message}", ex);
            }
        }

        private int DequeueItemsForDeletedTriggerFile(string triggerFilePath)
        {
            int removedCount = 0;
            var tempQueue = new List<FolderUploadInfo>();

            // Drain the queue to find and remove matching items
            while (uploadQueue.TryDequeue(out FolderUploadInfo item))
            {
                if (string.Equals(item.TriggerFilePath, triggerFilePath, StringComparison.OrdinalIgnoreCase))
                {
                    // Don't re-enqueue this item - it's being removed
                    removedCount++;
                    LogInfo($"Removed from queue: {item.FolderPath} (trigger file deleted: {Path.GetFileName(triggerFilePath)})");

                    // Log removal to database
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await LogUploadEventAsync("QUEUE_REMOVED", triggerFilePath, item.FolderPath, null, null,
                                "Upload removed from queue due to trigger file deletion");
                        }
                        catch (Exception ex)
                        {
                            LogError($"Failed to log queue removal to database: {ex.Message}", ex);
                        }
                    });
                }
                else
                {
                    // Keep this item - re-enqueue it
                    tempQueue.Add(item);
                }
            }

            // Re-enqueue all items that should remain
            foreach (var item in tempQueue)
            {
                uploadQueue.Enqueue(item);
            }

            return removedCount;
        }

        private void OnWatcherError(object sender, ErrorEventArgs e)
        {
            LogError($"FileSystemWatcher error: {e.GetException().Message}", e.GetException());

            // Try to restart the watcher
            try
            {
                LogInfo("Attempting to restart FileSystemWatcher...");
                watcher.EnableRaisingEvents = false;
                Thread.Sleep(1000);
                watcher.EnableRaisingEvents = true;
                LogInfo("FileSystemWatcher restarted successfully");
            }
            catch (Exception ex)
            {
                LogError($"Failed to restart FileSystemWatcher: {ex.Message}", ex);
            }
        }

        private void OnPollingTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                if (enableDetailedLogging)
                {
                    LogDebug("Polling timer elapsed - scanning for new files");
                }

                var rdyFiles = Directory.GetFiles(watchFolder, "*.Rdy", SearchOption.TopDirectoryOnly);

                foreach (var file in rdyFiles)
                {
                    ProcessDetectedFile(file, "Polling");
                }

                if (enableDetailedLogging)
                {
                    LogDebug($"Polling scan completed - found {rdyFiles.Length} .Rdy files");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error during polling scan: {ex.Message}", ex);
            }
        }

        private void OnQueueStatusTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                LogQueueStatus();
            }
            catch (Exception ex)
            {
                LogError($"Error during queue status logging: {ex.Message}", ex);
            }
        }

        private void ProcessDetectedFile(string filePath, string detectionMethod)
        {
            try
            {
                // Check if we've already processed this file recently
                lock (processedFilesLock)
                {
                    if (processedFiles.Contains(filePath))
                    {
                        if (enableDetailedLogging)
                        {
                            LogDebug($"File already processed recently: {filePath}");
                        }
                        return;
                    }
                    processedFiles.Add(filePath);
                }

                LogInfo($"Detected trigger file via {detectionMethod}: {filePath}. Will parse XML and upload after {waitTimeBeforeUpload.TotalMinutes} minutes");

                // Log trigger file detection to database
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await LogUploadEventAsync("TRIGGER_DETECTED", filePath, "", null, null, null);
                    }
                    catch (Exception ex)
                    {
                        LogError($"Failed to log trigger detection to database: {ex.Message}", ex);
                    }
                });

                // Wait a moment to ensure file is fully written
                Thread.Sleep(500);

                // Parse the XML file to get the folder path
                DateTime parseStartTime = DateTime.Now;
                string folderPath = ParseFolderPathFromXml(filePath);
                TimeSpan parseDuration = DateTime.Now - parseStartTime;

                if (!string.IsNullOrEmpty(folderPath))
                {
                    // Log successful file parsing to database
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await LogUploadEventAsync("FILE_PARSED", filePath, folderPath, null, null, null);
                        }
                        catch (Exception ex)
                        {
                            LogError($"Failed to log file parsing to database: {ex.Message}", ex);
                        }
                    });

                    // Queue folder for upload with creation timestamp
                    uploadQueue.Enqueue(new FolderUploadInfo
                    {
                        TriggerFilePath = filePath,
                        FolderPath = folderPath,
                        DetectionTime = DateTime.Now
                    });

                    LogInfo($"Parsed folder path from XML: {folderPath} (parse time: {parseDuration.TotalMilliseconds:F0}ms)");
                }
                else
                {
                    // Log parsing failure to database
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await LogUploadEventAsync("PARSING_FAILED", filePath, "", null, null, "Could not extract folder path from XML");
                        }
                        catch (Exception ex)
                        {
                            LogError($"Failed to log parsing failure to database: {ex.Message}", ex);
                        }
                    });

                    LogError($"Could not parse folder path from XML file: {filePath}");
                }

                // Clean up processed files list periodically
                if (processedFiles.Count > 1000)
                {
                    lock (processedFilesLock)
                    {
                        processedFiles.Clear();
                        LogDebug("Cleared processed files cache");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log processing error to database
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await LogUploadEventAsync("PROCESSING_ERROR", filePath, "", null, null, ex.Message);
                    }
                    catch (Exception dbEx)
                    {
                        LogError($"Failed to log processing error to database: {dbEx.Message}", dbEx);
                    }
                });

                LogError($"Error processing detected file {filePath}: {ex.Message}", ex);
            }
        }

        private async Task UploadFolderAsync(FolderUploadInfo folderInfo)
        {
            DateTime processStartTime = DateTime.Now;
            LogInfo($"Starting folder upload for {folderInfo.FolderPath}");

            // Try to lock the trigger file before starting upload
            bool fileLocked = await TryLockTriggerFileAsync(folderInfo.TriggerFilePath);
            if (!fileLocked)
            {
                string errorMessage = $"Cannot lock trigger file for upload: {folderInfo.TriggerFilePath}. File may be in use by another process.";
                LogError(errorMessage);

                // Log lock failure to database
                await LogUploadEventAsync("LOCK_FAILED", folderInfo.TriggerFilePath, folderInfo.FolderPath,
                    null, null, errorMessage);

                throw new InvalidOperationException(errorMessage);
            }

            try
            {
                // Get all files in the folder that match our criteria
                var filesToUpload = GetFilesToUpload(folderInfo.FolderPath);

                if (filesToUpload.Count == 0)
                {
                    LogWarn($"No files found to upload in folder {folderInfo.FolderPath}");

                    // Log upload event for empty folder
                    await LogUploadEventAsync("UPLOAD_EMPTY", folderInfo.TriggerFilePath, folderInfo.FolderPath, 0, 0);

                    // Log process completion for empty folder
                    TimeSpan emptyProcessDuration = DateTime.Now - processStartTime;
                    await LogUploadEventAsync("PROCESS_COMPLETED", folderInfo.TriggerFilePath, folderInfo.FolderPath, 0, 0,
                        $"Empty folder processed in {emptyProcessDuration.TotalSeconds:F1} seconds");
                    return;
                }

                LogInfo($"Found {filesToUpload.Count} files to upload from folder {folderInfo.FolderPath}");

                // Calculate total size for logging
                long totalSizeBytes = 0;
                try
                {
                    totalSizeBytes = filesToUpload.Sum(file => new FileInfo(file).Length);
                }
                catch (Exception ex)
                {
                    LogWarn($"Could not calculate total file size: {ex.Message}");
                }

                // Log upload start
                await LogUploadEventAsync("UPLOAD_START", folderInfo.TriggerFilePath, folderInfo.FolderPath,
                    filesToUpload.Count, totalSizeBytes);

                // Create SSH.Net connection info
                var connectionInfo = new ConnectionInfo(ftpHost, sftpPort, ftpUsername,
                    new PasswordAuthenticationMethod(ftpUsername, ftpPassword))
                {
                    Timeout = TimeSpan.FromSeconds(connectionTimeoutSeconds)
                };

                await ExecuteWithRetryAsync(async () =>
                {
                    using (var sftpClient = new SftpClient(connectionInfo))
                    {
                        // Set operation timeout
                        sftpClient.OperationTimeout = TimeSpan.FromSeconds(operationTimeoutSeconds);

                        // Connect with retry
                        sftpClient.Connect();
                        LogInfo($"Successfully connected to SFTP server: {ftpHost}:{sftpPort}");

                        // Verify host key if fingerprint is provided
                        if (!string.IsNullOrEmpty(fingerprint))
                        {
                            VerifyHostKey(sftpClient, fingerprint);
                        }

                        // Pre-create directory structure if configured and preserving folder structure
                        if (preCreateDirectories && preserveFolderStructure)
                        {
                            await PreCreateDirectoryStructureAsync(sftpClient, filesToUpload, folderInfo.FolderPath);
                        }

                        // Upload files with multithreading
                        await UploadFilesMultithreadedAsync(sftpClient, filesToUpload, folderInfo.FolderPath);
                    }
                }, $"SFTP connection and upload for {folderInfo.FolderPath}");

                LogInfo($"Successfully completed folder upload for {folderInfo.FolderPath}");

                // Calculate total process duration
                TimeSpan processDuration = DateTime.Now - processStartTime;

                // Log upload success
                await LogUploadEventAsync("UPLOAD_SUCCESS", folderInfo.TriggerFilePath, folderInfo.FolderPath,
                    filesToUpload.Count, totalSizeBytes);

                // Log process completion with performance metrics
                await LogUploadEventAsync("PROCESS_COMPLETED", folderInfo.TriggerFilePath, folderInfo.FolderPath,
                    filesToUpload.Count, totalSizeBytes,
                    $"Upload completed successfully in {processDuration.TotalSeconds:F1} seconds. " +
                    $"Average: {(totalSizeBytes / 1024.0 / 1024.0 / processDuration.TotalSeconds):F2} MB/s");

                // Delete trigger file if configured to do so
                if (deleteTriggerFileAfterUpload && File.Exists(folderInfo.TriggerFilePath))
                {
                    try
                    {
                        File.Delete(folderInfo.TriggerFilePath);
                        LogInfo($"Deleted trigger file: {folderInfo.TriggerFilePath}");
                    }
                    catch (Exception ex)
                    {
                        LogWarn($"Could not delete trigger file {folderInfo.TriggerFilePath}: {ex.Message}");
                    }
                }

                // Clean up deleted trigger files tracking for this completed upload
                lock (deletedTriggerFilesLock)
                {
                    if (deletedTriggerFiles.Remove(folderInfo.TriggerFilePath))
                    {
                        if (enableDetailedLogging)
                        {
                            LogDebug($"Removed {folderInfo.TriggerFilePath} from deleted trigger files tracking");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in folder upload for {folderInfo.FolderPath}: {ex.Message}", ex);

                // Calculate process duration for failed upload
                TimeSpan failedProcessDuration = DateTime.Now - processStartTime;

                // Log upload failure
                await LogUploadEventAsync("UPLOAD_FAILED", folderInfo.TriggerFilePath, folderInfo.FolderPath,
                    null, null, ex.Message);

                // Log process completion with failure details
                await LogUploadEventAsync("PROCESS_FAILED", folderInfo.TriggerFilePath, folderInfo.FolderPath,
                    null, null,
                    $"Upload failed after {failedProcessDuration.TotalSeconds:F1} seconds. Error: {ex.Message}");

                throw;
            }
            finally
            {
                // Always release the trigger file lock, regardless of success or failure
                try
                {
                    await ReleaseTriggerFileLockAsync(folderInfo.TriggerFilePath);
                }
                catch (Exception lockEx)
                {
                    LogError($"Error releasing trigger file lock: {lockEx.Message}", lockEx);
                }
            }
        }

        private List<string> GetFilesToUpload(string folderPath)
        {
            var filesToUpload = new List<string>();

            try
            {
                // Get all files in the folder
                var allFiles = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories);

                foreach (var file in allFiles)
                {
                    string fileName = Path.GetFileName(file);

                    // Check if file matches include patterns
                    bool includeFile = includeFilePatterns.Any(pattern =>
                        MatchesPattern(fileName, pattern));

                    // Check if file matches exclude patterns
                    bool excludeFile = excludeFilePatterns.Any(pattern =>
                        MatchesPattern(fileName, pattern));

                    if (includeFile && !excludeFile)
                    {
                        filesToUpload.Add(file);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error scanning folder {folderPath}: {ex.Message}", ex);
            }

            return filesToUpload;
        }

        private bool MatchesPattern(string fileName, string pattern)
        {
            // Simple wildcard matching - convert to regex
            string regexPattern = "^" + pattern.Replace("*", ".*").Replace("?", ".") + "$";
            return System.Text.RegularExpressions.Regex.IsMatch(fileName, regexPattern,
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }

        private void VerifyHostKey(SftpClient sftpClient, string expectedFingerprint)
        {
            try
            {
                // SSH.Net automatically verifies host keys during connection
                // For additional verification, we can check the server's host key
                var serverHostKey = sftpClient.ConnectionInfo.ServerVersion;
                LogInfo($"Connected to SFTP server. Server version: {serverHostKey}");

                // Note: SSH.Net handles host key verification automatically
                // If you need strict fingerprint verification, you would need to implement
                // a custom HostKeyEventArgs handler during connection setup
                LogDebug($"Host key verification completed for {ftpHost}");
            }
            catch (Exception ex)
            {
                LogWarn($"Host key verification warning: {ex.Message}");
                // Don't fail the connection for host key verification issues
                // SSH.Net handles this automatically
            }
        }

        private async Task PreCreateDirectoryStructureAsync(SftpClient sftpClient, List<string> filesToUpload, string baseFolderPath)
        {
            try
            {
                LogInfo("Pre-creating remote directory structure...");

                // Get all unique directory paths that need to be created
                var uniqueDirectories = new HashSet<string>();

                foreach (var filePath in filesToUpload)
                {
                    string relativePath = Path.GetRelativePath(baseFolderPath, filePath);
                    string remoteFilePath = ftpRemotePath.TrimEnd('/') + "/" + relativePath.Replace('\\', '/');
                    string remoteDir = Path.GetDirectoryName(remoteFilePath)?.Replace('\\', '/');

                    if (!string.IsNullOrEmpty(remoteDir))
                    {
                        // Add all parent directories to the set
                        string currentDir = remoteDir;
                        string baseRemotePath = ftpRemotePath.TrimEnd('/');

                        while (!string.IsNullOrEmpty(currentDir) && currentDir != baseRemotePath)
                        {
                            uniqueDirectories.Add(currentDir);
                            currentDir = Path.GetDirectoryName(currentDir)?.Replace('\\', '/');
                        }
                    }
                }

                // Sort directories by depth (shortest first) to create parent directories before children
                var sortedDirectories = uniqueDirectories
                    .OrderBy(dir => dir.Count(c => c == '/'))
                    .ToList();

                LogInfo($"Creating {sortedDirectories.Count} remote directories...");

                // Create directories sequentially to avoid conflicts
                foreach (var directory in sortedDirectories)
                {
                    try
                    {
                        if (!sftpClient.Exists(directory))
                        {
                            sftpClient.CreateDirectory(directory);
                            LogDebug($"Created remote directory: {directory}");
                        }
                        else
                        {
                            LogDebug($"Remote directory already exists: {directory}");
                        }
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("already exists") || ex.Message.Contains("File exists") ||
                            ex.Message.Contains("Failure"))
                        {
                            LogDebug($"Directory already exists (caught exception): {directory}");
                        }
                        else
                        {
                            LogWarn($"Could not create directory {directory}: {ex.Message}");
                        }
                    }
                }

                LogInfo("Completed pre-creating remote directory structure");
            }
            catch (Exception ex)
            {
                LogError($"Error pre-creating directory structure: {ex.Message}", ex);
                // Don't throw - continue with upload, individual file uploads will create directories as needed
            }

            await Task.CompletedTask;
        }

        private async Task UploadFilesMultithreadedAsync(SftpClient sftpClient, List<string> filesToUpload, string baseFolderPath)
        {
            // Create a semaphore to limit concurrent file uploads within this folder upload
            using (var fileUploadSemaphore = new SemaphoreSlim(maxConcurrentUploads, maxConcurrentUploads))
            {
                var uploadTasks = filesToUpload.Select(async filePath =>
                {
                    await fileUploadSemaphore.WaitAsync();
                    try
                    {
                        await ExecuteWithRetryAsync(
                            () => UploadSingleFileAsync(sftpClient, filePath, baseFolderPath),
                            $"file upload for {Path.GetFileName(filePath)}");
                    }
                    finally
                    {
                        fileUploadSemaphore.Release();
                    }
                });

                await Task.WhenAll(uploadTasks);
            }
        }

        private void EnsureRemoteDirectoryExists(SftpClient sftpClient, string remotePath)
        {
            try
            {
                // Normalize the path
                remotePath = remotePath.Replace('\\', '/').TrimEnd('/');

                // Skip if it's the root remote path
                string baseRemotePath = ftpRemotePath.TrimEnd('/');
                if (string.IsNullOrEmpty(remotePath) || remotePath == baseRemotePath)
                {
                    return;
                }

                LogDebug($"Ensuring remote directory exists: {remotePath}");

                // Use lock to prevent race conditions when multiple threads create directories
                lock (directoryCreationLock)
                {
                    try
                    {
                        // Check if directory already exists
                        if (sftpClient.Exists(remotePath))
                        {
                            LogDebug($"Remote directory already exists: {remotePath}");
                            return;
                        }

                        // Create parent directories recursively
                        string parentPath = Path.GetDirectoryName(remotePath)?.Replace('\\', '/');
                        if (!string.IsNullOrEmpty(parentPath) && parentPath != baseRemotePath)
                        {
                            EnsureRemoteDirectoryExists(sftpClient, parentPath);
                        }

                        // Create the directory
                        sftpClient.CreateDirectory(remotePath);
                        LogInfo($"Created remote directory: {remotePath}");
                    }
                    catch (Exception ex)
                    {
                        // Check if the error is because directory already exists
                        if (ex.Message.Contains("already exists") || ex.Message.Contains("File exists") ||
                            ex.Message.Contains("Failure"))
                        {
                            LogDebug($"Remote directory already exists (caught exception): {remotePath}");
                        }
                        else
                        {
                            LogError($"Error creating remote directory {remotePath}: {ex.Message}", ex);
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to ensure remote directory exists {remotePath}: {ex.Message}", ex);
                throw;
            }
        }

        private async Task UploadSingleFileAsync(SftpClient sftpClient, string filePath, string baseFolderPath)
        {
            DateTime fileUploadStartTime = DateTime.Now;
            long fileSizeBytes = 0;

            try
            {
                // Get file size for logging
                try
                {
                    fileSizeBytes = new FileInfo(filePath).Length;
                }
                catch (Exception ex)
                {
                    LogWarn($"Could not get file size for {filePath}: {ex.Message}");
                }

                LogDebug($"Uploading file: {filePath}");

                string remotePath;
                if (preserveFolderStructure)
                {
                    // Preserve the folder structure relative to the base folder
                    string relativePath = Path.GetRelativePath(baseFolderPath, filePath);
                    remotePath = ftpRemotePath.TrimEnd('/') + "/" + relativePath.Replace('\\', '/');
                }
                else
                {
                    // Upload all files to the same remote directory
                    string fileName = Path.GetFileName(filePath);
                    remotePath = ftpRemotePath.TrimEnd('/') + "/" + fileName;
                }

                // Ensure remote directory exists
                string remoteDir = Path.GetDirectoryName(remotePath)?.Replace('\\', '/');
                if (!string.IsNullOrEmpty(remoteDir))
                {
                    EnsureRemoteDirectoryExists(sftpClient, remoteDir);
                }

                // Upload the file using SSH.Net
                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    sftpClient.UploadFile(fileStream, remotePath);
                }

                // Calculate upload duration
                TimeSpan uploadDuration = DateTime.Now - fileUploadStartTime;

                LogInfo($"Successfully uploaded: {filePath} -> {remotePath} ({fileSizeBytes / 1024.0:F1} KB in {uploadDuration.TotalSeconds:F1}s)");

                // Log individual file completion to database (if enabled)
                await LogFileEventAsync("FILE_COMPLETED", filePath, remotePath, fileSizeBytes);
            }
            catch (Exception ex)
            {
                // Calculate failed upload duration
                TimeSpan failedUploadDuration = DateTime.Now - fileUploadStartTime;

                LogError($"Error uploading file {filePath}: {ex.Message}", ex);

                // Log individual file failure to database (if enabled)
                await LogFileEventAsync("FILE_FAILED", filePath, null, fileSizeBytes, ex.Message);

                throw;
            }

            await Task.CompletedTask;
        }

        private string ParseFolderPathFromXml(string xmlFilePath)
        {
            try
            {
                LogDebug($"Parsing XML file: {xmlFilePath}");

                // Wait a moment to ensure file is fully written
                Thread.Sleep(1000);

                // Load the XML document
                XDocument doc = XDocument.Load(xmlFilePath);

                string folderPath = null;

                // Try to find the folder path using the configured element name
                if (!string.IsNullOrEmpty(xmlFolderPathAttribute))
                {
                    // Look for an attribute with the specified name
                    var elementWithAttribute = doc.Descendants()
                        .FirstOrDefault(e => e.Attribute(xmlFolderPathAttribute) != null);

                    if (elementWithAttribute != null)
                    {
                        folderPath = elementWithAttribute.Attribute(xmlFolderPathAttribute).Value;
                    }
                }
                else
                {
                    // Look for an element with the specified name
                    var element = doc.Descendants(xmlFolderPathElement).FirstOrDefault();
                    if (element != null)
                    {
                        folderPath = element.Value;
                    }
                }

                // If not found with configured names, try common element names
                if (string.IsNullOrEmpty(folderPath))
                {
                    var commonElementNames = new[] { "FolderPath", "Path", "Directory", "Folder", "SourcePath", "UploadPath" };

                    foreach (var elementName in commonElementNames)
                    {
                        var element = doc.Descendants(elementName).FirstOrDefault();
                        if (element != null && !string.IsNullOrEmpty(element.Value))
                        {
                            folderPath = element.Value;
                            LogDebug($"Found folder path using element '{elementName}': {folderPath}");
                            break;
                        }
                    }
                }

                // If still not found, try common attribute names
                if (string.IsNullOrEmpty(folderPath))
                {
                    var commonAttributeNames = new[] { "path", "folder", "directory", "source", "upload" };

                    foreach (var attrName in commonAttributeNames)
                    {
                        var elementWithAttr = doc.Descendants()
                            .FirstOrDefault(e => e.Attribute(attrName) != null);

                        if (elementWithAttr != null)
                        {
                            folderPath = elementWithAttr.Attribute(attrName).Value;
                            LogDebug($"Found folder path using attribute '{attrName}': {folderPath}");
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(folderPath))
                {
                    // Validate that the folder exists
                    if (Directory.Exists(folderPath))
                    {
                        WriteToEventLog($"Successfully parsed and validated folder path: {folderPath}");
                        return folderPath;
                    }
                    else
                    {
                        WriteToEventLog($"Parsed folder path does not exist: {folderPath}", true);
                    }
                }
                else
                {
                    WriteToEventLog($"Could not find folder path in XML file. XML content: {doc.ToString()}", true);
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"Error parsing XML file {xmlFilePath}: {ex.Message}", true);
            }

            return null;
        }

        private void LogInfo(string message)
        {
            logger.Info(message);
        }

        #region Database Logging Methods

        private async Task LogServiceEventAsync(string eventType, string description, string details = null)
        {
            if (!enableDatabaseLogging || !logServiceEvents)
                return;

            try
            {
                await ExecuteDatabaseCommandAsync(async (connection) =>
                {
                    const string sql = @"
                        INSERT INTO ServiceEvents (EventTime, EventType, Description, Details, MachineName, ServiceName)
                        VALUES (@EventTime, @EventType, @Description, @Details, @MachineName, @ServiceName)";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EventTime", DateTime.Now);
                        command.Parameters.AddWithValue("@EventType", eventType);
                        command.Parameters.AddWithValue("@Description", description);
                        command.Parameters.AddWithValue("@Details", details ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@MachineName", Environment.MachineName);
                        command.Parameters.AddWithValue("@ServiceName", ServiceName);

                        await command.ExecuteNonQueryAsync();
                    }
                });

                if (enableDetailedLogging)
                {
                    LogDebug($"Database: Logged service event - {eventType}: {description}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to log service event to database: {ex.Message}", ex);
            }
        }

        private async Task LogUploadEventAsync(string eventType, string triggerFilePath, string folderPath,
            int? fileCount = null, long? totalSizeBytes = null, string errorMessage = null)
        {
            if (!enableDatabaseLogging || !logUploadEvents)
                return;

            try
            {
                await ExecuteDatabaseCommandAsync(async (connection) =>
                {
                    const string sql = @"
                        INSERT INTO UploadEvents (EventTime, EventType, TriggerFilePath, FolderPath, FileCount,
                                                TotalSizeBytes, ErrorMessage, MachineName, ServiceName)
                        VALUES (@EventTime, @EventType, @TriggerFilePath, @FolderPath, @FileCount,
                               @TotalSizeBytes, @ErrorMessage, @MachineName, @ServiceName)";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EventTime", DateTime.Now);
                        command.Parameters.AddWithValue("@EventType", eventType);
                        command.Parameters.AddWithValue("@TriggerFilePath", triggerFilePath);
                        command.Parameters.AddWithValue("@FolderPath", folderPath);
                        command.Parameters.AddWithValue("@FileCount", fileCount ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@TotalSizeBytes", totalSizeBytes ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ErrorMessage", errorMessage ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@MachineName", Environment.MachineName);
                        command.Parameters.AddWithValue("@ServiceName", ServiceName);

                        await command.ExecuteNonQueryAsync();
                    }
                });

                if (enableDetailedLogging)
                {
                    LogDebug($"Database: Logged upload event - {eventType}: {folderPath}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to log upload event to database: {ex.Message}", ex);
            }
        }

        private async Task LogFileEventAsync(string eventType, string filePath, string remotePath = null,
            long? fileSizeBytes = null, string errorMessage = null)
        {
            if (!enableDatabaseLogging || !logFileEvents)
                return;

            try
            {
                await ExecuteDatabaseCommandAsync(async (connection) =>
                {
                    const string sql = @"
                        INSERT INTO FileEvents (EventTime, EventType, LocalFilePath, RemoteFilePath,
                                              FileSizeBytes, ErrorMessage, MachineName, ServiceName)
                        VALUES (@EventTime, @EventType, @LocalFilePath, @RemoteFilePath,
                               @FileSizeBytes, @ErrorMessage, @MachineName, @ServiceName)";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EventTime", DateTime.Now);
                        command.Parameters.AddWithValue("@EventType", eventType);
                        command.Parameters.AddWithValue("@LocalFilePath", filePath);
                        command.Parameters.AddWithValue("@RemoteFilePath", remotePath ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@FileSizeBytes", fileSizeBytes ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ErrorMessage", errorMessage ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@MachineName", Environment.MachineName);
                        command.Parameters.AddWithValue("@ServiceName", ServiceName);

                        await command.ExecuteNonQueryAsync();
                    }
                });

                if (enableDetailedLogging)
                {
                    LogDebug($"Database: Logged file event - {eventType}: {Path.GetFileName(filePath)}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to log file event to database: {ex.Message}", ex);
            }
        }

        private async Task ExecuteDatabaseCommandAsync(Func<SqlConnection, Task> operation)
        {
            if (string.IsNullOrEmpty(databaseConnectionString))
            {
                if (enableDetailedLogging)
                {
                    LogDebug("Database logging skipped - no connection string configured");
                }
                return;
            }

            using (var connection = new SqlConnection(databaseConnectionString))
            {
                connection.Open();
                await operation(connection);
            }
        }

        #endregion

        #region File Locking Methods

        private async Task<bool> TryLockTriggerFileAsync(string triggerFilePath)
        {
            if (!enableTriggerFileLocking)
            {
                if (enableDetailedLogging)
                {
                    LogDebug($"File locking disabled - skipping lock for: {triggerFilePath}");
                }
                return true; // Consider it "locked" if locking is disabled
            }

            try
            {
                if (enableDetailedLogging)
                {
                    LogDebug($"Attempting to lock trigger file: {triggerFilePath}");
                }

                // Check if file is already locked by this service
                if (lockedTriggerFiles.ContainsKey(triggerFilePath))
                {
                    LogWarn($"Trigger file already locked by this service: {triggerFilePath}");
                    return true; // Already locked by us
                }

                // Try to open the file with exclusive access
                var fileStream = new FileStream(
                    triggerFilePath,
                    FileMode.Open,
                    FileAccess.Read,
                    FileShare.None, // Exclusive access - no sharing
                    bufferSize: 4096,
                    useAsync: false);

                // Store the lock
                if (lockedTriggerFiles.TryAdd(triggerFilePath, fileStream))
                {
                    LogInfo($"Successfully locked trigger file: {triggerFilePath}");

                    // Log file lock to database
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await LogUploadEventAsync("FILE_LOCKED", triggerFilePath, "", null, null,
                                $"Trigger file locked for upload process");
                        }
                        catch (Exception ex)
                        {
                            LogError($"Failed to log file lock to database: {ex.Message}", ex);
                        }
                    });

                    return true;
                }
                else
                {
                    // Failed to add to dictionary, close the stream
                    fileStream?.Dispose();
                    LogWarn($"Failed to register lock for trigger file: {triggerFilePath}");
                    return false;
                }
            }
            catch (IOException ioEx)
            {
                LogWarn($"Cannot lock trigger file (file in use): {triggerFilePath} - {ioEx.Message}");
                return false;
            }
            catch (UnauthorizedAccessException uaEx)
            {
                LogWarn($"Cannot lock trigger file (access denied): {triggerFilePath} - {uaEx.Message}");
                return false;
            }
            catch (Exception ex)
            {
                LogError($"Error locking trigger file {triggerFilePath}: {ex.Message}", ex);
                return false;
            }
        }

        private async Task ReleaseTriggerFileLockAsync(string triggerFilePath)
        {
            if (!enableTriggerFileLocking)
            {
                if (enableDetailedLogging)
                {
                    LogDebug($"File locking disabled - skipping unlock for: {triggerFilePath}");
                }
                return;
            }

            try
            {
                if (lockedTriggerFiles.TryRemove(triggerFilePath, out FileStream fileStream))
                {
                    fileStream?.Dispose();
                    LogInfo($"Released lock on trigger file: {triggerFilePath}");

                    // Log file unlock to database
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await LogUploadEventAsync("FILE_UNLOCKED", triggerFilePath, "", null, null,
                                $"Trigger file lock released after upload process");
                        }
                        catch (Exception ex)
                        {
                            LogError($"Failed to log file unlock to database: {ex.Message}", ex);
                        }
                    });
                }
                else
                {
                    if (enableDetailedLogging)
                    {
                        LogDebug($"No lock found to release for trigger file: {triggerFilePath}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error releasing lock on trigger file {triggerFilePath}: {ex.Message}", ex);
            }
        }

        private void ReleaseAllTriggerFileLocks()
        {
            try
            {
                LogInfo($"Releasing all trigger file locks ({lockedTriggerFiles.Count} files)");

                foreach (var kvp in lockedTriggerFiles.ToArray())
                {
                    try
                    {
                        kvp.Value?.Dispose();
                        LogDebug($"Released lock on: {kvp.Key}");
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error releasing lock on {kvp.Key}: {ex.Message}", ex);
                    }
                }

                lockedTriggerFiles.Clear();
                LogInfo("All trigger file locks released");
            }
            catch (Exception ex)
            {
                LogError($"Error releasing all trigger file locks: {ex.Message}", ex);
            }
        }

        #endregion

        #region Queue Status Logging

        private void LogQueueStatus()
        {
            try
            {
                int queueCount = uploadQueue.Count;

                if (queueCount == 0)
                {
                    LogInfo("QUEUE STATUS: Empty - No uploads pending");
                    return;
                }

                LogInfo($"QUEUE STATUS: {queueCount} upload(s) pending");

                // Get queue items for detailed logging
                var queueItems = uploadQueue.ToArray();

                for (int i = 0; i < Math.Min(queueItems.Length, 10); i++) // Show up to 10 items
                {
                    var item = queueItems[i];
                    var waitTime = DateTime.Now - item.DetectionTime;
                    var remainingWait = waitTimeBeforeUpload - waitTime;

                    string status;
                    if (remainingWait > TimeSpan.Zero)
                    {
                        status = $"Waiting {remainingWait.TotalMinutes:F1}m";
                    }
                    else
                    {
                        status = "Ready for upload";
                    }

                    // Check if this item's trigger file has been deleted
                    bool isDeleted;
                    lock (deletedTriggerFilesLock)
                    {
                        isDeleted = deletedTriggerFiles.Contains(item.TriggerFilePath);
                    }

                    string deletedIndicator = isDeleted ? " [DELETED - PENDING REMOVAL]" : "";

                    LogInfo($"  [{i + 1}] {Path.GetFileName(item.TriggerFilePath)} -> {item.FolderPath} ({status}){deletedIndicator}");
                }

                if (queueItems.Length > 10)
                {
                    LogInfo($"  ... and {queueItems.Length - 10} more items");
                }

                // Log queue statistics with deleted file analysis
                var readyCount = queueItems.Count(item => (DateTime.Now - item.DetectionTime) >= waitTimeBeforeUpload);
                var waitingCount = queueCount - readyCount;

                // Count items that correspond to deleted trigger files
                int queuedDeletedCount;
                lock (deletedTriggerFilesLock)
                {
                    queuedDeletedCount = queueItems.Count(item => deletedTriggerFiles.Contains(item.TriggerFilePath));
                }

                LogInfo($"QUEUE SUMMARY: {readyCount} ready, {waitingCount} waiting, {queuedDeletedCount} pending removal, {consecutiveFailureCount} consecutive failures");

                if (processingPaused)
                {
                    LogWarn("QUEUE STATUS: Processing is PAUSED due to consecutive failures");
                }

                // Log deleted trigger files tracking with details
                string[] deletedTriggerFilesList;
                int deletedCount;
                lock (deletedTriggerFilesLock)
                {
                    deletedCount = deletedTriggerFiles.Count;
                    deletedTriggerFilesList = deletedTriggerFiles.ToArray();
                }

                if (deletedCount > 0)
                {
                    LogInfo($"QUEUE STATUS: {deletedCount} deleted trigger file(s) being tracked for queue removal:");

                    for (int i = 0; i < Math.Min(deletedTriggerFilesList.Length, 5); i++) // Show up to 5 deleted files
                    {
                        string fileName = Path.GetFileName(deletedTriggerFilesList[i]);
                        LogInfo($"  - {fileName} (deleted, pending queue removal)");
                    }

                    if (deletedTriggerFilesList.Length > 5)
                    {
                        LogInfo($"  ... and {deletedTriggerFilesList.Length - 5} more deleted files");
                    }
                }

                // Log locked files count
                int lockedCount = lockedTriggerFiles.Count;
                if (lockedCount > 0)
                {
                    LogInfo($"QUEUE STATUS: {lockedCount} trigger file(s) currently locked");
                }

                // Log to database if enabled
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Build detailed information about deleted files for database
                        string deletedFilesDetails = "";
                        if (deletedCount > 0)
                        {
                            var deletedFileNames = deletedTriggerFilesList.Take(10).Select(Path.GetFileName);
                            deletedFilesDetails = $", DeletedFiles: [{string.Join(", ", deletedFileNames)}]";
                            if (deletedTriggerFilesList.Length > 10)
                            {
                                deletedFilesDetails += $" +{deletedTriggerFilesList.Length - 10} more";
                            }
                        }

                        await LogServiceEventAsync("QUEUE_STATUS",
                            $"Queue status: {queueCount} pending, {readyCount} ready, {waitingCount} waiting, {queuedDeletedCount} pending removal",
                            $"ConsecutiveFailures: {consecutiveFailureCount}, ProcessingPaused: {processingPaused}, " +
                            $"DeletedTracking: {deletedCount}, QueuedDeleted: {queuedDeletedCount}, LockedFiles: {lockedCount}{deletedFilesDetails}");
                    }
                    catch (Exception ex)
                    {
                        LogError($"Failed to log queue status to database: {ex.Message}", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                LogError($"Error generating queue status: {ex.Message}", ex);
            }
        }

        #endregion

        private void LogError(string message, Exception ex = null)
        {
            if (ex != null)
                logger.Error(message, ex);
            else
                logger.Error(message);
        }

        private void LogWarn(string message)
        {
            logger.Warn(message);
        }

        private void LogDebug(string message)
        {
            logger.Debug(message);
        }

        // Class to store folder upload information with detection time
        private class FolderUploadInfo
        {
            public string TriggerFilePath { get; set; }
            public string FolderPath { get; set; }
            public DateTime DetectionTime { get; set; }
        }
    }
}