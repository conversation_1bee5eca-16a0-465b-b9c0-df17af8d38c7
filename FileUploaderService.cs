using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.ServiceProcess;
using System.Configuration;
using WinSCP;
using System.Linq;
using System.Collections.Generic;
using System.Xml;
using System.Xml.Linq;

namespace SftpFileUploaderService
{
    public class FileUploaderService : ServiceBase
    {
        // Configuration
        private string ftpHost;
        private string ftpUsername;
        private string ftpPassword;
        private string ftpRemotePath;
        private string watchFolder;
        private string fingerprint;
        private int maxConcurrentUploads;
        private TimeSpan waitTimeBeforeUpload;
        private string[] includeFilePatterns;
        private string[] excludeFilePatterns;
        private bool deleteTriggerFileAfterUpload;
        private bool preserveFolderStructure;
        private string xmlFolderPathElement;
        private string xmlFolderPathAttribute;

        // Service components
        private FileSystemWatcher watcher;
        private ConcurrentQueue<FolderUploadInfo> uploadQueue;
        private SemaphoreSlim throttler;
        private CancellationTokenSource cancellationTokenSource;

        public FileUploaderService()
        {
            ServiceName = "SftpFileUploaderService";
            CanStop = true;
            CanPauseAndContinue = false;
            AutoLog = true;
        }

        protected override void OnStart(string[] args)
        {
            // Load configuration from App.config
            LoadConfiguration();

            // Initialize components
            uploadQueue = new ConcurrentQueue<FolderUploadInfo>();
            throttler = new SemaphoreSlim(maxConcurrentUploads);
            cancellationTokenSource = new CancellationTokenSource();

            // Set up file system watcher
            watcher = new FileSystemWatcher(watchFolder)
            {
                Filter = "*.Rdy",
                NotifyFilter = NotifyFilters.FileName | NotifyFilters.LastWrite,
                EnableRaisingEvents = true
            };

            watcher.Created += (sender, e) =>
            {
                WriteToEventLog($"Detected trigger file: {e.FullPath}. Will parse XML and upload after {waitTimeBeforeUpload.TotalMinutes} minutes");

                try
                {
                    // Parse the XML file to get the folder path
                    string folderPath = ParseFolderPathFromXml(e.FullPath);

                    if (!string.IsNullOrEmpty(folderPath))
                    {
                        // Queue folder for upload with creation timestamp
                        uploadQueue.Enqueue(new FolderUploadInfo
                        {
                            TriggerFilePath = e.FullPath,
                            FolderPath = folderPath,
                            DetectionTime = DateTime.Now
                        });

                        WriteToEventLog($"Parsed folder path from XML: {folderPath}");
                    }
                    else
                    {
                        WriteToEventLog($"Could not parse folder path from XML file: {e.FullPath}", true);
                    }
                }
                catch (Exception ex)
                {
                    WriteToEventLog($"Error parsing XML trigger file {e.FullPath}: {ex.Message}", true);
                }
            };

            // Start background processing
            Task.Run(() => ProcessQueueContinuouslyAsync(cancellationTokenSource.Token));

            WriteToEventLog($"Service started. Monitoring folder: {watchFolder} for *.Rdy trigger files");
        }

        protected override void OnStop()
        {
            // Signal cancellation and cleanup
            cancellationTokenSource.Cancel();
            watcher.EnableRaisingEvents = false;
            watcher.Dispose();
            throttler.Dispose();
            cancellationTokenSource.Dispose();

            WriteToEventLog("Service stopped");
        }

        private void LoadConfiguration()
        {
            ftpHost = ConfigurationManager.AppSettings["FtpHost"] ?? "sftp.example.com";
            ftpUsername = ConfigurationManager.AppSettings["FtpUsername"] ?? "username";
            ftpPassword = ConfigurationManager.AppSettings["FtpPassword"] ?? "password";
            ftpRemotePath = ConfigurationManager.AppSettings["FtpRemotePath"] ?? "/upload/";
            watchFolder = ConfigurationManager.AppSettings["WatchFolder"] ?? @"C:\path\to\watch\folder";
            fingerprint = ConfigurationManager.AppSettings["SshFingerprint"] ?? "ssh-rsa 2048 xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx";

            if (!int.TryParse(ConfigurationManager.AppSettings["MaxConcurrentUploads"], out maxConcurrentUploads))
                maxConcurrentUploads = 3;

            int waitMinutes;
            if (!int.TryParse(ConfigurationManager.AppSettings["WaitMinutesBeforeUpload"], out waitMinutes))
                waitMinutes = 20;

            waitTimeBeforeUpload = TimeSpan.FromMinutes(waitMinutes);

            // New configuration options for folder upload
            string includePatterns = ConfigurationManager.AppSettings["IncludeFilePatterns"] ?? "*.*";
            includeFilePatterns = includePatterns.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                .Select(p => p.Trim()).ToArray();

            string excludePatterns = ConfigurationManager.AppSettings["ExcludeFilePatterns"] ?? "*.Rdy;*.tmp;*.log";
            excludeFilePatterns = excludePatterns.Split(new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                .Select(p => p.Trim()).ToArray();

            if (!bool.TryParse(ConfigurationManager.AppSettings["DeleteTriggerFileAfterUpload"], out deleteTriggerFileAfterUpload))
                deleteTriggerFileAfterUpload = true;

            if (!bool.TryParse(ConfigurationManager.AppSettings["PreserveFolderStructure"], out preserveFolderStructure))
                preserveFolderStructure = false;

            // XML parsing configuration
            xmlFolderPathElement = ConfigurationManager.AppSettings["XmlFolderPathElement"] ?? "FolderPath";
            xmlFolderPathAttribute = ConfigurationManager.AppSettings["XmlFolderPathAttribute"] ?? "";
        }

        private async Task ProcessQueueContinuouslyAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    if (uploadQueue.TryPeek(out FolderUploadInfo folderInfo))
                    {
                        // Check if the wait time has elapsed
                        TimeSpan elapsed = DateTime.Now - folderInfo.DetectionTime;

                        if (elapsed >= waitTimeBeforeUpload)
                        {
                            // Remove from queue
                            if (uploadQueue.TryDequeue(out folderInfo))
                            {
                                await throttler.WaitAsync(cancellationToken);

                                // Start a new task to handle the folder upload
                                _ = Task.Run(async () =>
                                {
                                    try
                                    {
                                        WriteToEventLog($"Wait time elapsed for folder {folderInfo.FolderPath}, starting upload");
                                        await UploadFolderAsync(folderInfo);
                                    }
                                    catch (Exception ex)
                                    {
                                        WriteToEventLog($"Error uploading folder {folderInfo.FolderPath}: {ex.Message}", true);
                                    }
                                    finally
                                    {
                                        throttler.Release();
                                    }
                                }, cancellationToken);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteToEventLog($"Error in queue processing: {ex.Message}", true);
                }

                // Sleep for a short time before checking again
                await Task.Delay(5000, cancellationToken); // Check every 5 seconds
            }
        }

        private async Task UploadFolderAsync(FolderUploadInfo folderInfo)
        {
            WriteToEventLog($"Starting folder upload for {folderInfo.FolderPath}");

            try
            {
                // Get all files in the folder that match our criteria
                var filesToUpload = GetFilesToUpload(folderInfo.FolderPath);

                if (filesToUpload.Count == 0)
                {
                    WriteToEventLog($"No files found to upload in folder {folderInfo.FolderPath}");
                    return;
                }

                WriteToEventLog($"Found {filesToUpload.Count} files to upload from folder {folderInfo.FolderPath}");

                // Create session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = ftpHost,
                    UserName = ftpUsername,
                    Password = ftpPassword,
                    SshHostKeyFingerprint = fingerprint
                };

                using (Session session = new Session())
                {
                    // Connect
                    session.Open(sessionOptions);

                    // Upload files with multithreading
                    await UploadFilesMultithreadedAsync(session, filesToUpload, folderInfo.FolderPath);
                }

                WriteToEventLog($"Successfully completed folder upload for {folderInfo.FolderPath}");

                // Delete trigger file if configured to do so
                if (deleteTriggerFileAfterUpload && File.Exists(folderInfo.TriggerFilePath))
                {
                    try
                    {
                        File.Delete(folderInfo.TriggerFilePath);
                        WriteToEventLog($"Deleted trigger file: {folderInfo.TriggerFilePath}");
                    }
                    catch (Exception ex)
                    {
                        WriteToEventLog($"Warning: Could not delete trigger file {folderInfo.TriggerFilePath}: {ex.Message}", true);
                    }
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"Error in folder upload for {folderInfo.FolderPath}: {ex.Message}", true);
                throw;
            }
        }

        private List<string> GetFilesToUpload(string folderPath)
        {
            var filesToUpload = new List<string>();

            try
            {
                // Get all files in the folder
                var allFiles = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories);

                foreach (var file in allFiles)
                {
                    string fileName = Path.GetFileName(file);

                    // Check if file matches include patterns
                    bool includeFile = includeFilePatterns.Any(pattern =>
                        MatchesPattern(fileName, pattern));

                    // Check if file matches exclude patterns
                    bool excludeFile = excludeFilePatterns.Any(pattern =>
                        MatchesPattern(fileName, pattern));

                    if (includeFile && !excludeFile)
                    {
                        filesToUpload.Add(file);
                    }
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"Error scanning folder {folderPath}: {ex.Message}", true);
            }

            return filesToUpload;
        }

        private bool MatchesPattern(string fileName, string pattern)
        {
            // Simple wildcard matching - convert to regex
            string regexPattern = "^" + pattern.Replace("*", ".*").Replace("?", ".") + "$";
            return System.Text.RegularExpressions.Regex.IsMatch(fileName, regexPattern,
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }

        private async Task UploadFilesMultithreadedAsync(Session session, List<string> filesToUpload, string baseFolderPath)
        {
            // Create a semaphore to limit concurrent file uploads within this folder upload
            using (var fileUploadSemaphore = new SemaphoreSlim(maxConcurrentUploads, maxConcurrentUploads))
            {
                var uploadTasks = filesToUpload.Select(async filePath =>
                {
                    await fileUploadSemaphore.WaitAsync();
                    try
                    {
                        await UploadSingleFileAsync(session, filePath, baseFolderPath);
                    }
                    finally
                    {
                        fileUploadSemaphore.Release();
                    }
                });

                await Task.WhenAll(uploadTasks);
            }
        }

        private async Task UploadSingleFileAsync(Session session, string filePath, string baseFolderPath)
        {
            try
            {
                WriteToEventLog($"Uploading file: {filePath}");

                string remotePath;
                if (preserveFolderStructure)
                {
                    // Preserve the folder structure relative to the base folder
                    string relativePath = Path.GetRelativePath(baseFolderPath, filePath);
                    remotePath = ftpRemotePath.TrimEnd('/') + "/" + relativePath.Replace('\\', '/');
                }
                else
                {
                    // Upload all files to the same remote directory
                    string fileName = Path.GetFileName(filePath);
                    remotePath = ftpRemotePath.TrimEnd('/') + "/" + fileName;
                }

                // Ensure remote directory exists if preserving folder structure
                if (preserveFolderStructure)
                {
                    string remoteDir = Path.GetDirectoryName(remotePath).Replace('\\', '/');
                    if (!string.IsNullOrEmpty(remoteDir) && remoteDir != ftpRemotePath.TrimEnd('/'))
                    {
                        try
                        {
                            session.CreateDirectory(remoteDir);
                        }
                        catch (Exception)
                        {
                            // Directory might already exist, ignore error
                        }
                    }
                }

                // Upload the file
                TransferOptions transferOptions = new TransferOptions
                {
                    TransferMode = TransferMode.Binary
                };

                TransferOperationResult result = session.PutFiles(filePath, remotePath, false, transferOptions);
                result.Check();

                WriteToEventLog($"Successfully uploaded: {filePath} -> {remotePath}");
            }
            catch (Exception ex)
            {
                WriteToEventLog($"Error uploading file {filePath}: {ex.Message}", true);
                throw;
            }

            await Task.CompletedTask;
        }

        private string ParseFolderPathFromXml(string xmlFilePath)
        {
            try
            {
                WriteToEventLog($"Parsing XML file: {xmlFilePath}");

                // Wait a moment to ensure file is fully written
                Thread.Sleep(1000);

                // Load the XML document
                XDocument doc = XDocument.Load(xmlFilePath);

                string folderPath = null;

                // Try to find the folder path using the configured element name
                if (!string.IsNullOrEmpty(xmlFolderPathAttribute))
                {
                    // Look for an attribute with the specified name
                    var elementWithAttribute = doc.Descendants()
                        .FirstOrDefault(e => e.Attribute(xmlFolderPathAttribute) != null);

                    if (elementWithAttribute != null)
                    {
                        folderPath = elementWithAttribute.Attribute(xmlFolderPathAttribute).Value;
                    }
                }
                else
                {
                    // Look for an element with the specified name
                    var element = doc.Descendants(xmlFolderPathElement).FirstOrDefault();
                    if (element != null)
                    {
                        folderPath = element.Value;
                    }
                }

                // If not found with configured names, try common element names
                if (string.IsNullOrEmpty(folderPath))
                {
                    var commonElementNames = new[] { "FolderPath", "Path", "Directory", "Folder", "SourcePath", "UploadPath" };

                    foreach (var elementName in commonElementNames)
                    {
                        var element = doc.Descendants(elementName).FirstOrDefault();
                        if (element != null && !string.IsNullOrEmpty(element.Value))
                        {
                            folderPath = element.Value;
                            WriteToEventLog($"Found folder path using element '{elementName}': {folderPath}");
                            break;
                        }
                    }
                }

                // If still not found, try common attribute names
                if (string.IsNullOrEmpty(folderPath))
                {
                    var commonAttributeNames = new[] { "path", "folder", "directory", "source", "upload" };

                    foreach (var attrName in commonAttributeNames)
                    {
                        var elementWithAttr = doc.Descendants()
                            .FirstOrDefault(e => e.Attribute(attrName) != null);

                        if (elementWithAttr != null)
                        {
                            folderPath = elementWithAttr.Attribute(attrName).Value;
                            WriteToEventLog($"Found folder path using attribute '{attrName}': {folderPath}");
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(folderPath))
                {
                    // Validate that the folder exists
                    if (Directory.Exists(folderPath))
                    {
                        WriteToEventLog($"Successfully parsed and validated folder path: {folderPath}");
                        return folderPath;
                    }
                    else
                    {
                        WriteToEventLog($"Parsed folder path does not exist: {folderPath}", true);
                    }
                }
                else
                {
                    WriteToEventLog($"Could not find folder path in XML file. XML content: {doc.ToString()}", true);
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"Error parsing XML file {xmlFilePath}: {ex.Message}", true);
            }

            return null;
        }

        private void WriteToEventLog(string message, bool isError = false)
        {
            try
            {
                if (isError)
                    EventLog.WriteEntry(message, System.Diagnostics.EventLogEntryType.Error);
                else
                    EventLog.WriteEntry(message, System.Diagnostics.EventLogEntryType.Information);
            }
            catch
            {
                // Fallback if event log writing fails
                Console.WriteLine(message);
            }
        }

        // Class to store folder upload information with detection time
        private class FolderUploadInfo
        {
            public string TriggerFilePath { get; set; }
            public string FolderPath { get; set; }
            public DateTime DetectionTime { get; set; }
        }
    }
}