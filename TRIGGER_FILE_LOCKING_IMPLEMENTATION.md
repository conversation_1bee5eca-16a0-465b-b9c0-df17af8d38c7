# Trigger File Locking Implementation

## Overview
Implemented comprehensive trigger file locking mechanism to prevent conflicts and ensure data integrity during the upload process. This enhancement locks trigger files exclusively during upload operations, preventing other processes from modifying or deleting them while uploads are in progress.

## Key Features Implemented

### 1. Exclusive File Locking
**Purpose**: Prevent concurrent access to trigger files during upload operations

**Implementation**:
```csharp
private async Task<bool> TryLockTriggerFileAsync(string triggerFilePath)
{
    // Try to open the file with exclusive access
    var fileStream = new FileStream(
        triggerFilePath,
        FileMode.Open,
        FileAccess.Read,
        FileShare.None, // Exclusive access - no sharing
        bufferSize: 4096,
        useAsync: false);

    // Store the lock
    if (lockedTriggerFiles.TryAdd(triggerFilePath, fileStream))
    {
        LogInfo($"Successfully locked trigger file: {triggerFilePath}");
        return true;
    }
    
    return false;
}
```

### 2. Automatic Lock Management
**Lock Acquisition**: Automatically locks trigger files before starting upload process
**Lock Release**: Automatically releases locks after upload completion (success or failure)
**Service Shutdown**: Releases all locks when service stops

### 3. Conflict Detection and Handling
**Graceful Failure**: Handles cases where files are already locked by other processes
**Error Logging**: Comprehensive logging of lock failures and conflicts
**Database Tracking**: Records lock events for monitoring and troubleshooting

## Implementation Details

### Configuration Options
```xml
<!-- File Locking Settings -->
<!-- Enable trigger file locking during upload process (recommended: true) -->
<add key="EnableTriggerFileLocking" value="true" />

<!-- File lock timeout in seconds (default: 30) -->
<add key="FileLockTimeoutSeconds" value="30" />
```

### Core Components

#### 1. Lock Management Infrastructure
```csharp
// File locking configuration
private bool enableTriggerFileLocking;
private int fileLockTimeoutSeconds;
private readonly ConcurrentDictionary<string, FileStream> lockedTriggerFiles = new ConcurrentDictionary<string, FileStream>();
```

#### 2. Lock Acquisition Method
```csharp
private async Task<bool> TryLockTriggerFileAsync(string triggerFilePath)
{
    if (!enableTriggerFileLocking)
    {
        return true; // Consider it "locked" if locking is disabled
    }

    try
    {
        // Check if file is already locked by this service
        if (lockedTriggerFiles.ContainsKey(triggerFilePath))
        {
            LogWarn($"Trigger file already locked by this service: {triggerFilePath}");
            return true; // Already locked by us
        }

        // Try to open the file with exclusive access
        var fileStream = new FileStream(
            triggerFilePath,
            FileMode.Open,
            FileAccess.Read,
            FileShare.None, // Exclusive access - no sharing
            bufferSize: 4096,
            useAsync: false);

        // Store the lock
        if (lockedTriggerFiles.TryAdd(triggerFilePath, fileStream))
        {
            LogInfo($"Successfully locked trigger file: {triggerFilePath}");
            
            // Log file lock to database
            await LogUploadEventAsync("FILE_LOCKED", triggerFilePath, "", null, null, 
                $"Trigger file locked for upload process");

            return true;
        }
        else
        {
            // Failed to add to dictionary, close the stream
            fileStream?.Dispose();
            LogWarn($"Failed to register lock for trigger file: {triggerFilePath}");
            return false;
        }
    }
    catch (IOException ioEx)
    {
        LogWarn($"Cannot lock trigger file (file in use): {triggerFilePath} - {ioEx.Message}");
        return false;
    }
    catch (UnauthorizedAccessException uaEx)
    {
        LogWarn($"Cannot lock trigger file (access denied): {triggerFilePath} - {uaEx.Message}");
        return false;
    }
    catch (Exception ex)
    {
        LogError($"Error locking trigger file {triggerFilePath}: {ex.Message}", ex);
        return false;
    }
}
```

#### 3. Lock Release Method
```csharp
private async Task ReleaseTriggerFileLockAsync(string triggerFilePath)
{
    if (!enableTriggerFileLocking)
    {
        return;
    }

    try
    {
        if (lockedTriggerFiles.TryRemove(triggerFilePath, out FileStream fileStream))
        {
            fileStream?.Dispose();
            LogInfo($"Released lock on trigger file: {triggerFilePath}");

            // Log file unlock to database
            await LogUploadEventAsync("FILE_UNLOCKED", triggerFilePath, "", null, null, 
                $"Trigger file lock released after upload process");
        }
    }
    catch (Exception ex)
    {
        LogError($"Error releasing lock on trigger file {triggerFilePath}: {ex.Message}", ex);
    }
}
```

#### 4. Service Shutdown Cleanup
```csharp
private void ReleaseAllTriggerFileLocks()
{
    try
    {
        LogInfo($"Releasing all trigger file locks ({lockedTriggerFiles.Count} files)");

        foreach (var kvp in lockedTriggerFiles.ToArray())
        {
            try
            {
                kvp.Value?.Dispose();
                LogDebug($"Released lock on: {kvp.Key}");
            }
            catch (Exception ex)
            {
                LogError($"Error releasing lock on {kvp.Key}: {ex.Message}", ex);
            }
        }

        lockedTriggerFiles.Clear();
        LogInfo("All trigger file locks released");
    }
    catch (Exception ex)
    {
        LogError($"Error releasing all trigger file locks: {ex.Message}", ex);
    }
}
```

### Integration with Upload Process

#### 1. Lock Before Upload
```csharp
private async Task UploadFolderAsync(FolderUploadInfo folderInfo)
{
    DateTime processStartTime = DateTime.Now;
    LogInfo($"Starting folder upload for {folderInfo.FolderPath}");

    // Try to lock the trigger file before starting upload
    bool fileLocked = await TryLockTriggerFileAsync(folderInfo.TriggerFilePath);
    if (!fileLocked)
    {
        string errorMessage = $"Cannot lock trigger file for upload: {folderInfo.TriggerFilePath}. File may be in use by another process.";
        LogError(errorMessage);
        
        // Log lock failure to database
        await LogUploadEventAsync("LOCK_FAILED", folderInfo.TriggerFilePath, folderInfo.FolderPath, 
            null, null, errorMessage);
        
        throw new InvalidOperationException(errorMessage);
    }

    try
    {
        // ... upload process ...
    }
    finally
    {
        // Always release the trigger file lock, regardless of success or failure
        try
        {
            await ReleaseTriggerFileLockAsync(folderInfo.TriggerFilePath);
        }
        catch (Exception lockEx)
        {
            LogError($"Error releasing trigger file lock: {lockEx.Message}", lockEx);
        }
    }
}
```

## Database Events for File Locking

### New Event Types
- **FILE_LOCKED**: Trigger file successfully locked for upload
- **LOCK_FAILED**: Failed to lock trigger file (file in use)
- **FILE_UNLOCKED**: Trigger file lock released after upload

### Database Monitoring Queries

#### Check File Locking Events
```sql
-- Query file locking events and failures
SELECT EventTime, EventType, TriggerFilePath, ErrorMessage
FROM UploadEvents 
WHERE EventType IN ('FILE_LOCKED', 'LOCK_FAILED', 'FILE_UNLOCKED')
ORDER BY EventTime DESC;
```

#### Identify Files with Locking Issues
```sql
-- Identify files with locking issues
SELECT TriggerFilePath, COUNT(*) AS LockFailureCount, MAX(EventTime) AS LastFailure
FROM UploadEvents 
WHERE EventType = 'LOCK_FAILED'
GROUP BY TriggerFilePath
ORDER BY LockFailureCount DESC;
```

#### Detect Lock Leaks
```sql
-- Check for files that were locked but never unlocked (potential lock leaks)
SELECT locked.TriggerFilePath, locked.EventTime AS LockedTime
FROM UploadEvents locked
LEFT JOIN UploadEvents unlocked ON locked.TriggerFilePath = unlocked.TriggerFilePath 
    AND unlocked.EventType = 'FILE_UNLOCKED' 
    AND unlocked.EventTime > locked.EventTime
WHERE locked.EventType = 'FILE_LOCKED'
  AND unlocked.Id IS NULL
  AND locked.EventTime >= DATEADD(HOUR, -24, GETDATE())
ORDER BY locked.EventTime DESC;
```

## Operational Benefits

### 1. Data Integrity
- **Prevents Corruption**: Ensures trigger files cannot be modified during processing
- **Atomic Operations**: Upload process has exclusive access to trigger file
- **Consistent State**: Prevents race conditions between multiple processes

### 2. Conflict Resolution
- **Graceful Handling**: Detects and handles file conflicts without crashing
- **Clear Messaging**: Provides detailed error messages for troubleshooting
- **Retry Capability**: Can retry operations when locks become available

### 3. Monitoring and Troubleshooting
- **Lock Tracking**: Complete audit trail of file locking operations
- **Failure Analysis**: Detailed logging of lock failures and causes
- **Performance Impact**: Minimal overhead with configurable options

### 4. Reliability
- **Automatic Cleanup**: Ensures locks are always released
- **Service Recovery**: Releases all locks on service shutdown
- **Error Isolation**: Lock failures don't crash the service

## Configuration Best Practices

### Production Settings
```xml
<!-- Enable file locking for data integrity -->
<add key="EnableTriggerFileLocking" value="true" />

<!-- Conservative timeout for production -->
<add key="FileLockTimeoutSeconds" value="30" />
```

### Development/Testing Settings
```xml
<!-- May disable for testing scenarios -->
<add key="EnableTriggerFileLocking" value="false" />

<!-- Shorter timeout for development -->
<add key="FileLockTimeoutSeconds" value="10" />
```

## Error Handling Scenarios

### 1. File Already in Use
- **Detection**: IOException when attempting to lock
- **Response**: Log warning and fail gracefully
- **Recovery**: Can retry when file becomes available

### 2. Access Denied
- **Detection**: UnauthorizedAccessException
- **Response**: Log error with permission details
- **Recovery**: Requires permission adjustment

### 3. Lock Timeout
- **Detection**: Configurable timeout mechanism
- **Response**: Release lock and log timeout
- **Recovery**: Automatic retry with exponential backoff

## Files Modified

### Enhanced Files
- **FileUploaderService.cs**: Complete file locking implementation
- **App.config.example**: File locking configuration options
- **DatabaseSchema.sql**: New event types and monitoring queries
- **README.md**: Updated feature documentation

### New Documentation
- **TRIGGER_FILE_LOCKING_IMPLEMENTATION.md**: This comprehensive implementation guide

## Summary

The trigger file locking implementation provides enterprise-grade data integrity and conflict prevention for the FileUploaderService. By implementing exclusive file locking with comprehensive error handling, database tracking, and automatic cleanup, the service now ensures that trigger files cannot be corrupted or interfered with during upload operations, significantly improving reliability and operational stability.
