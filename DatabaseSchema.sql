-- FileUploaderService Database Schema
-- This script creates the necessary tables for logging service and upload events

-- Create database (uncomment if needed)
-- CREATE DATABASE FileUploaderDB;
-- GO
-- USE FileUploaderDB;
-- GO

-- Service Events Table
-- Logs service start/stop and other service-level events
CREATE TABLE ServiceEvents (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    EventTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    EventType NVARCHAR(50) NOT NULL,
    Description NVARCHAR(500) NOT NULL,
    Details NVARCHAR(MAX) NULL,
    MachineName NVARCHAR(100) NOT NULL,
    ServiceName NVARCHAR(100) NOT NULL,
    
    INDEX IX_ServiceEvents_EventTime (EventTime),
    INDEX IX_ServiceEvents_EventType (EventType),
    INDEX IX_ServiceEvents_MachineName (MachineName)
);

-- Upload Events Table  
-- Logs upload process start/end events
CREATE TABLE UploadEvents (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    EventTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    EventType NVARCHAR(50) NOT NULL,
    TriggerFilePath NVARCHAR(500) NOT NULL,
    FolderPath NVARCHAR(500) NOT NULL,
    FileCount INT NULL,
    TotalSizeBytes BIGINT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    MachineName NVARCHAR(100) NOT NULL,
    ServiceName NVARCHAR(100) NOT NULL,
    
    INDEX IX_UploadEvents_EventTime (EventTime),
    INDEX IX_UploadEvents_EventType (EventType),
    INDEX IX_UploadEvents_TriggerFilePath (TriggerFilePath),
    INDEX IX_UploadEvents_MachineName (MachineName)
);

-- File Events Table
-- Logs individual file upload events (optional, can be verbose)
CREATE TABLE FileEvents (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    EventTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    EventType NVARCHAR(50) NOT NULL,
    LocalFilePath NVARCHAR(500) NOT NULL,
    RemoteFilePath NVARCHAR(500) NULL,
    FileSizeBytes BIGINT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    MachineName NVARCHAR(100) NOT NULL,
    ServiceName NVARCHAR(100) NOT NULL,
    
    INDEX IX_FileEvents_EventTime (EventTime),
    INDEX IX_FileEvents_EventType (EventType),
    INDEX IX_FileEvents_LocalFilePath (LocalFilePath),
    INDEX IX_FileEvents_MachineName (MachineName)
);

-- Create views for common queries

-- Service Status View
CREATE VIEW vw_ServiceStatus AS
SELECT 
    MachineName,
    ServiceName,
    MAX(CASE WHEN EventType = 'SERVICE_START' THEN EventTime END) AS LastStartTime,
    MAX(CASE WHEN EventType = 'SERVICE_STOP' THEN EventTime END) AS LastStopTime,
    CASE 
        WHEN MAX(CASE WHEN EventType = 'SERVICE_START' THEN EventTime END) > 
             ISNULL(MAX(CASE WHEN EventType = 'SERVICE_STOP' THEN EventTime END), '1900-01-01')
        THEN 'RUNNING'
        ELSE 'STOPPED'
    END AS CurrentStatus
FROM ServiceEvents
WHERE EventType IN ('SERVICE_START', 'SERVICE_STOP')
GROUP BY MachineName, ServiceName;

-- Upload Summary View
CREATE VIEW vw_UploadSummary AS
SELECT 
    CAST(EventTime AS DATE) AS UploadDate,
    MachineName,
    COUNT(CASE WHEN EventType = 'UPLOAD_START' THEN 1 END) AS UploadsStarted,
    COUNT(CASE WHEN EventType = 'UPLOAD_SUCCESS' THEN 1 END) AS UploadsSucceeded,
    COUNT(CASE WHEN EventType = 'UPLOAD_FAILED' THEN 1 END) AS UploadsFailed,
    SUM(CASE WHEN EventType = 'UPLOAD_SUCCESS' THEN FileCount ELSE 0 END) AS TotalFilesUploaded,
    SUM(CASE WHEN EventType = 'UPLOAD_SUCCESS' THEN TotalSizeBytes ELSE 0 END) AS TotalBytesUploaded
FROM UploadEvents
GROUP BY CAST(EventTime AS DATE), MachineName;

-- Recent Upload Activity View
CREATE VIEW vw_RecentUploadActivity AS
SELECT TOP 100
    EventTime,
    EventType,
    TriggerFilePath,
    FolderPath,
    FileCount,
    TotalSizeBytes,
    ErrorMessage,
    MachineName
FROM UploadEvents
ORDER BY EventTime DESC;

-- Create stored procedures for common operations

-- Get Service Status
CREATE PROCEDURE sp_GetServiceStatus
    @MachineName NVARCHAR(100) = NULL
AS
BEGIN
    SELECT 
        MachineName,
        ServiceName,
        LastStartTime,
        LastStopTime,
        CurrentStatus,
        CASE 
            WHEN CurrentStatus = 'RUNNING' AND LastStartTime IS NOT NULL
            THEN DATEDIFF(MINUTE, LastStartTime, GETDATE())
            ELSE NULL
        END AS UptimeMinutes
    FROM vw_ServiceStatus
    WHERE (@MachineName IS NULL OR MachineName = @MachineName)
    ORDER BY MachineName, ServiceName;
END;

-- Get Upload Statistics
CREATE PROCEDURE sp_GetUploadStatistics
    @StartDate DATE = NULL,
    @EndDate DATE = NULL,
    @MachineName NVARCHAR(100) = NULL
AS
BEGIN
    SET @StartDate = ISNULL(@StartDate, CAST(GETDATE() AS DATE));
    SET @EndDate = ISNULL(@EndDate, CAST(GETDATE() AS DATE));
    
    SELECT 
        UploadDate,
        MachineName,
        UploadsStarted,
        UploadsSucceeded,
        UploadsFailed,
        TotalFilesUploaded,
        TotalBytesUploaded,
        CASE 
            WHEN UploadsStarted > 0 
            THEN CAST(UploadsSucceeded AS FLOAT) / UploadsStarted * 100
            ELSE 0
        END AS SuccessPercentage
    FROM vw_UploadSummary
    WHERE UploadDate BETWEEN @StartDate AND @EndDate
      AND (@MachineName IS NULL OR MachineName = @MachineName)
    ORDER BY UploadDate DESC, MachineName;
END;

-- Get Failed Uploads
CREATE PROCEDURE sp_GetFailedUploads
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @MachineName NVARCHAR(100) = NULL
AS
BEGIN
    SET @StartDate = ISNULL(@StartDate, DATEADD(DAY, -7, GETDATE()));
    SET @EndDate = ISNULL(@EndDate, GETDATE());
    
    SELECT 
        EventTime,
        TriggerFilePath,
        FolderPath,
        FileCount,
        TotalSizeBytes,
        ErrorMessage,
        MachineName
    FROM UploadEvents
    WHERE EventType = 'UPLOAD_FAILED'
      AND EventTime BETWEEN @StartDate AND @EndDate
      AND (@MachineName IS NULL OR MachineName = @MachineName)
    ORDER BY EventTime DESC;
END;

-- Cleanup old records procedure
CREATE PROCEDURE sp_CleanupOldRecords
    @RetentionDays INT = 90
AS
BEGIN
    DECLARE @CutoffDate DATETIME2 = DATEADD(DAY, -@RetentionDays, GETDATE());
    DECLARE @DeletedServiceEvents INT;
    DECLARE @DeletedUploadEvents INT;
    DECLARE @DeletedFileEvents INT;
    
    -- Delete old service events
    DELETE FROM ServiceEvents WHERE EventTime < @CutoffDate;
    SET @DeletedServiceEvents = @@ROWCOUNT;
    
    -- Delete old upload events
    DELETE FROM UploadEvents WHERE EventTime < @CutoffDate;
    SET @DeletedUploadEvents = @@ROWCOUNT;
    
    -- Delete old file events
    DELETE FROM FileEvents WHERE EventTime < @CutoffDate;
    SET @DeletedFileEvents = @@ROWCOUNT;
    
    -- Return cleanup summary
    SELECT 
        @CutoffDate AS CutoffDate,
        @DeletedServiceEvents AS DeletedServiceEvents,
        @DeletedUploadEvents AS DeletedUploadEvents,
        @DeletedFileEvents AS DeletedFileEvents;
END;

-- Grant permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT ON ServiceEvents TO [FileUploaderServiceUser];
-- GRANT SELECT, INSERT ON UploadEvents TO [FileUploaderServiceUser];
-- GRANT SELECT, INSERT ON FileEvents TO [FileUploaderServiceUser];
-- GRANT EXECUTE ON sp_GetServiceStatus TO [FileUploaderServiceUser];
-- GRANT EXECUTE ON sp_GetUploadStatistics TO [FileUploaderServiceUser];
-- GRANT EXECUTE ON sp_GetFailedUploads TO [FileUploaderServiceUser];

-- Event Types Documentation
/*
Service Events:
- SERVICE_START: Service startup
- SERVICE_STOP: Service shutdown
- QUEUE_STATUS: Periodic queue status logging (every 30 seconds by default)
  * Includes detailed deleted trigger files list and queue item status
  * Shows which queue items correspond to deleted trigger files
  * Provides comprehensive queue health and performance metrics
- QUEUE_CLEANUP: Summary of queue cleanup operations when trigger files are deleted
  * Records number of uploads removed and cleanup method (immediate vs deferred)

Upload Events:
- TRIGGER_DETECTED: Trigger file detected and queued for processing
- FILE_PARSED: Trigger file successfully parsed, folder path extracted
- PARSING_FAILED: Failed to parse trigger file or extract folder path
- PROCESSING_ERROR: Error during trigger file processing
- FILE_LOCKED: Trigger file locked for exclusive access during upload
- LOCK_FAILED: Failed to lock trigger file (file in use by another process)
- FILE_UNLOCKED: Trigger file lock released after upload completion
- UPLOAD_START: Upload process started for a folder
- UPLOAD_SUCCESS: Upload process completed successfully
- UPLOAD_FAILED: Upload process failed after all retries
- UPLOAD_EMPTY: No files found to upload in the specified folder
- QUEUE_REMOVED: Individual upload removed from queue due to trigger file deletion
- PROCESS_COMPLETED: Entire process completed successfully (includes performance metrics)
- PROCESS_FAILED: Entire process failed (includes failure details)

File Events (if enabled):
- FILE_COMPLETED: Individual file uploaded successfully
- FILE_FAILED: Individual file upload failed
*/

-- Sample queries for testing

-- Check service status
-- SELECT * FROM vw_ServiceStatus;

-- Get upload summary for today
-- SELECT * FROM vw_UploadSummary WHERE UploadDate = CAST(GETDATE() AS DATE);

-- Get recent upload activity
-- SELECT * FROM vw_RecentUploadActivity;

-- Get failed uploads from last 24 hours
-- EXEC sp_GetFailedUploads @StartDate = DATEADD(HOUR, -24, GETDATE());

-- Get upload statistics for last 7 days
-- EXEC sp_GetUploadStatistics @StartDate = DATEADD(DAY, -7, GETDATE());

-- Cleanup records older than 30 days
-- EXEC sp_CleanupOldRecords @RetentionDays = 30;

-- Query trigger file detection and parsing events
-- SELECT EventTime, EventType, TriggerFilePath, FolderPath, ErrorMessage
-- FROM UploadEvents
-- WHERE EventType IN ('TRIGGER_DETECTED', 'FILE_PARSED', 'PARSING_FAILED', 'PROCESSING_ERROR')
-- ORDER BY EventTime DESC;

-- Query process completion events with performance metrics
-- SELECT EventTime, EventType, TriggerFilePath, FolderPath, FileCount, TotalSizeBytes, ErrorMessage
-- FROM UploadEvents
-- WHERE EventType IN ('PROCESS_COMPLETED', 'PROCESS_FAILED')
-- ORDER BY EventTime DESC;

-- Get complete process timeline for a specific trigger file
-- SELECT EventTime, EventType, FolderPath, FileCount, TotalSizeBytes, ErrorMessage
-- FROM UploadEvents
-- WHERE TriggerFilePath = 'C:\path\to\trigger.Rdy'
-- ORDER BY EventTime;

-- Query file locking events and failures
-- SELECT EventTime, EventType, TriggerFilePath, ErrorMessage
-- FROM UploadEvents
-- WHERE EventType IN ('FILE_LOCKED', 'LOCK_FAILED', 'FILE_UNLOCKED')
-- ORDER BY EventTime DESC;

-- Identify files with locking issues
-- SELECT TriggerFilePath, COUNT(*) AS LockFailureCount, MAX(EventTime) AS LastFailure
-- FROM UploadEvents
-- WHERE EventType = 'LOCK_FAILED'
-- GROUP BY TriggerFilePath
-- ORDER BY LockFailureCount DESC;

-- Check for files that were locked but never unlocked (potential lock leaks)
-- SELECT locked.TriggerFilePath, locked.EventTime AS LockedTime
-- FROM UploadEvents locked
-- LEFT JOIN UploadEvents unlocked ON locked.TriggerFilePath = unlocked.TriggerFilePath
--     AND unlocked.EventType = 'FILE_UNLOCKED'
--     AND unlocked.EventTime > locked.EventTime
-- WHERE locked.EventType = 'FILE_LOCKED'
--   AND unlocked.Id IS NULL
--   AND locked.EventTime >= DATEADD(HOUR, -24, GETDATE())
-- ORDER BY locked.EventTime DESC;

-- Query queue status events for monitoring
-- SELECT EventTime, Description, Details
-- FROM ServiceEvents
-- WHERE EventType = 'QUEUE_STATUS'
-- ORDER BY EventTime DESC;

-- Monitor queue trends over time
-- SELECT
--     DATEPART(HOUR, EventTime) AS Hour,
--     AVG(CAST(SUBSTRING(Description, CHARINDEX(':', Description) + 2, CHARINDEX(' pending', Description) - CHARINDEX(':', Description) - 2) AS INT)) AS AvgQueueSize
-- FROM ServiceEvents
-- WHERE EventType = 'QUEUE_STATUS'
--   AND EventTime >= DATEADD(DAY, -1, GETDATE())
-- GROUP BY DATEPART(HOUR, EventTime)
-- ORDER BY Hour;

-- Check for processing issues (paused or high failure counts)
-- SELECT EventTime, Description, Details
-- FROM ServiceEvents
-- WHERE EventType = 'QUEUE_STATUS'
--   AND (Details LIKE '%ProcessingPaused: True%' OR Details LIKE '%ConsecutiveFailures: [5-9]%' OR Details LIKE '%ConsecutiveFailures: [1-9][0-9]%')
-- ORDER BY EventTime DESC;

-- Monitor deleted trigger files and queue removal activity
-- SELECT EventTime, Description, Details
-- FROM ServiceEvents
-- WHERE EventType = 'QUEUE_STATUS'
--   AND (Details LIKE '%DeletedTracking: [1-9]%' OR Details LIKE '%QueuedDeleted: [1-9]%' OR Details LIKE '%DeletedFiles:%')
-- ORDER BY EventTime DESC;

-- Extract deleted files information from queue status logs
-- SELECT
--     EventTime,
--     Description,
--     SUBSTRING(Details, CHARINDEX('DeletedFiles: [', Details) + 14,
--               CHARINDEX(']', Details, CHARINDEX('DeletedFiles: [', Details)) - CHARINDEX('DeletedFiles: [', Details) - 14) AS DeletedFilesList
-- FROM ServiceEvents
-- WHERE EventType = 'QUEUE_STATUS'
--   AND Details LIKE '%DeletedFiles: [%'
-- ORDER BY EventTime DESC;

-- Monitor queue cleanup operations
-- SELECT EventTime, Description, Details
-- FROM ServiceEvents
-- WHERE EventType = 'QUEUE_CLEANUP'
-- ORDER BY EventTime DESC;

-- Monitor individual queue removals
-- SELECT EventTime, EventType, TriggerFilePath, FolderPath, ErrorMessage
-- FROM UploadEvents
-- WHERE EventType = 'QUEUE_REMOVED'
-- ORDER BY EventTime DESC;

-- Analyze queue cleanup efficiency
-- SELECT
--     CAST(EventTime AS DATE) AS Date,
--     COUNT(*) AS CleanupOperations,
--     SUM(CAST(SUBSTRING(Description, CHARINDEX('Removed ', Description) + 8,
--                        CHARINDEX(' upload', Description) - CHARINDEX('Removed ', Description) - 8) AS INT)) AS TotalRemovedUploads,
--     AVG(CAST(SUBSTRING(Description, CHARINDEX('Removed ', Description) + 8,
--                        CHARINDEX(' upload', Description) - CHARINDEX('Removed ', Description) - 8) AS INT)) AS AvgRemovedPerOperation
-- FROM ServiceEvents
-- WHERE EventType = 'QUEUE_CLEANUP'
--   AND EventTime >= DATEADD(DAY, -7, GETDATE())
-- GROUP BY CAST(EventTime AS DATE)
-- ORDER BY Date DESC;
