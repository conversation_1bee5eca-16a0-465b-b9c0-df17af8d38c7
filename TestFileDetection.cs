using System;
using System.IO;
using System.Threading;

namespace SftpFileUploaderService.Test
{
    /// <summary>
    /// Simple test utility to create trigger files for testing file detection
    /// </summary>
    class TestFileDetection
    {
        static void Main(string[] args)
        {
            Console.WriteLine("File Detection Test Utility");
            Console.WriteLine("===========================");

            if (args.Length < 2)
            {
                Console.WriteLine("Usage: TestFileDetection.exe <WatchFolder> <TestDataFolder>");
                Console.WriteLine("Example: TestFileDetection.exe C:\\Upload\\TriggerFiles C:\\TestData");
                return;
            }

            string watchFolder = args[0];
            string testDataFolder = args[1];

            if (!Directory.Exists(watchFolder))
            {
                Console.WriteLine($"ERROR: Watch folder does not exist: {watchFolder}");
                return;
            }

            if (!Directory.Exists(testDataFolder))
            {
                Console.WriteLine($"Creating test data folder: {testDataFolder}");
                Directory.CreateDirectory(testDataFolder);
                
                // Create some test files
                File.WriteAllText(Path.Combine(testDataFolder, "test1.txt"), "Test file 1");
                File.WriteAllText(Path.Combine(testDataFolder, "test2.pdf"), "Test PDF content");
                File.WriteAllText(Path.Combine(testDataFolder, "test3.xml"), "<?xml version=\"1.0\"?><test>data</test>");
            }

            Console.WriteLine($"Watch Folder: {watchFolder}");
            Console.WriteLine($"Test Data Folder: {testDataFolder}");
            Console.WriteLine();

            while (true)
            {
                Console.WriteLine("Choose an option:");
                Console.WriteLine("1. Create single trigger file");
                Console.WriteLine("2. Create multiple trigger files (stress test)");
                Console.WriteLine("3. Create trigger file with delay");
                Console.WriteLine("4. Exit");
                Console.Write("Enter choice (1-4): ");

                string choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        CreateSingleTriggerFile(watchFolder, testDataFolder);
                        break;
                    case "2":
                        CreateMultipleTriggerFiles(watchFolder, testDataFolder);
                        break;
                    case "3":
                        CreateTriggerFileWithDelay(watchFolder, testDataFolder);
                        break;
                    case "4":
                        return;
                    default:
                        Console.WriteLine("Invalid choice. Please try again.");
                        break;
                }
                Console.WriteLine();
            }
        }

        static void CreateSingleTriggerFile(string watchFolder, string testDataFolder)
        {
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string triggerFile = Path.Combine(watchFolder, $"test_{timestamp}.Rdy");

            string xmlContent = $@"<?xml version=""1.0"" encoding=""utf-8""?>
<UploadRequest>
    <FolderPath>{testDataFolder}</FolderPath>
    <RequestId>TEST_{timestamp}</RequestId>
    <Timestamp>{DateTime.Now:yyyy-MM-ddTHH:mm:ss}</Timestamp>
    <Description>Test trigger file created by TestFileDetection utility</Description>
</UploadRequest>";

            File.WriteAllText(triggerFile, xmlContent);
            Console.WriteLine($"Created trigger file: {triggerFile}");
            Console.WriteLine("Check service logs for detection message.");
        }

        static void CreateMultipleTriggerFiles(string watchFolder, string testDataFolder)
        {
            Console.Write("How many trigger files to create? ");
            if (int.TryParse(Console.ReadLine(), out int count) && count > 0 && count <= 100)
            {
                Console.WriteLine($"Creating {count} trigger files...");
                
                for (int i = 1; i <= count; i++)
                {
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
                    string triggerFile = Path.Combine(watchFolder, $"stress_test_{i:D3}_{timestamp}.Rdy");

                    string xmlContent = $@"<?xml version=""1.0"" encoding=""utf-8""?>
<UploadRequest>
    <FolderPath>{testDataFolder}</FolderPath>
    <RequestId>STRESS_TEST_{i:D3}_{timestamp}</RequestId>
    <Timestamp>{DateTime.Now:yyyy-MM-ddTHH:mm:ss}</Timestamp>
    <Description>Stress test trigger file {i} of {count}</Description>
</UploadRequest>";

                    File.WriteAllText(triggerFile, xmlContent);
                    Console.WriteLine($"Created {i}/{count}: {Path.GetFileName(triggerFile)}");
                    
                    // Small delay to avoid overwhelming the system
                    Thread.Sleep(100);
                }
                
                Console.WriteLine($"Created {count} trigger files. Check service logs for detection messages.");
            }
            else
            {
                Console.WriteLine("Invalid count. Please enter a number between 1 and 100.");
            }
        }

        static void CreateTriggerFileWithDelay(string watchFolder, string testDataFolder)
        {
            Console.Write("Enter delay in seconds before creating file: ");
            if (int.TryParse(Console.ReadLine(), out int delay) && delay >= 0 && delay <= 300)
            {
                Console.WriteLine($"Will create trigger file in {delay} seconds...");
                
                for (int i = delay; i > 0; i--)
                {
                    Console.Write($"\rCountdown: {i:D2} seconds ");
                    Thread.Sleep(1000);
                }
                
                Console.WriteLine("\rCreating trigger file now...        ");
                CreateSingleTriggerFile(watchFolder, testDataFolder);
            }
            else
            {
                Console.WriteLine("Invalid delay. Please enter a number between 0 and 300.");
            }
        }
    }
}
