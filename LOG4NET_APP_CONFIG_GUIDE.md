# log4net Configuration in App.config Guide

## Overview
The FileUploaderService now uses log4net configuration embedded in the app.config file instead of a separate log4net.config file. This consolidates all configuration into a single file for easier management and deployment.

## Configuration Structure

### 1. Configuration Sections Declaration
```xml
<configSections>
  <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
</configSections>
```
**Purpose**: Tells .NET how to parse the log4net configuration section.

### 2. log4net Configuration Section
The log4net configuration is embedded directly in the app.config file:
```xml
<log4net>
  <!-- Appenders and loggers configuration -->
</log4net>
```

## Appender Configuration

### 1. File Appender (General Logs)
```xml
<appender name="FileAppender" type="log4net.Appender.RollingFileAppender">
  <file value="logs\SftpFileUploader.log" />
  <appendToFile value="true" />
  <rollingStyle value="Size" />
  <maxSizeRollBackups value="10" />
  <maximumFileSize value="10MB" />
  <staticLogFileName value="true" />
  <layout type="log4net.Layout.PatternLayout">
    <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
  </layout>
</appender>
```

**Features**:
- **Rolling by size**: Creates new files when 10MB limit is reached
- **Backup files**: Keeps 10 backup files
- **Pattern**: Includes timestamp, thread, log level, logger name, and message

### 2. Error File Appender (Errors and Warnings Only)
```xml
<appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
  <file value="logs\SftpFileUploader_Errors.log" />
  <appendToFile value="true" />
  <rollingStyle value="Size" />
  <maxSizeRollBackups value="5" />
  <maximumFileSize value="5MB" />
  <staticLogFileName value="true" />
  <threshold value="WARN" />
  <layout type="log4net.Layout.PatternLayout">
    <conversionPattern value="%date [%thread] %-5level %logger - %message%newline%exception" />
  </layout>
</appender>
```

**Features**:
- **Error-only**: Only logs WARN and ERROR level messages
- **Exception details**: Includes full exception stack traces
- **Smaller files**: 5MB limit with 5 backups for focused error tracking

### 3. Console Appender (Development)
```xml
<appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
  <layout type="log4net.Layout.PatternLayout">
    <conversionPattern value="%date %-5level - %message%newline" />
  </layout>
</appender>
```

**Features**:
- **Development use**: Outputs to console for debugging
- **Simple format**: Minimal pattern for readability
- **Disabled by default**: Uncomment in root logger to enable

### 4. Event Log Appender (Windows Service Integration)
```xml
<appender name="EventLogAppender" type="log4net.Appender.EventLogAppender">
  <logName value="Application" />
  <applicationName value="SftpFileUploaderService" />
  <threshold value="WARN" />
  <layout type="log4net.Layout.PatternLayout">
    <conversionPattern value="%date %-5level %logger - %message" />
  </layout>
</appender>
```

**Features**:
- **Windows integration**: Logs to Windows Event Log
- **Warning and errors only**: Threshold set to WARN
- **Service monitoring**: Integrates with Windows service management

## Logger Configuration

### 1. Root Logger
```xml
<root>
  <level value="INFO" />
  <appender-ref ref="FileAppender" />
  <appender-ref ref="ErrorFileAppender" />
  <appender-ref ref="EventLogAppender" />
</root>
```

**Features**:
- **Default level**: INFO and above (INFO, WARN, ERROR, FATAL)
- **Multiple outputs**: Logs to file, error file, and event log
- **Global scope**: Applies to all loggers unless overridden

### 2. Service-Specific Logger
```xml
<logger name="SftpFileUploaderService.FileUploaderService" additivity="false">
  <level value="DEBUG" />
  <appender-ref ref="FileAppender" />
  <appender-ref ref="ErrorFileAppender" />
  <appender-ref ref="EventLogAppender" />
</logger>
```

**Features**:
- **Debug level**: Captures all log messages including DEBUG
- **Non-additive**: Doesn't inherit from root logger (prevents duplicate logs)
- **Service-specific**: Only applies to the main service class

## Initialization in Code

### Service Constructor
```csharp
public FileUploaderService()
{
    ServiceName = "SftpFileUploaderService";
    CanStop = true;
    CanPauseAndContinue = false;
    AutoLog = true;

    // Initialize log4net from app.config
    XmlConfigurator.Configure();
}
```

**Key Points**:
- **XmlConfigurator.Configure()**: Reads log4net config from app.config
- **No file parameter**: Automatically uses app.config
- **Early initialization**: Called in constructor before any logging

## Log File Structure

### Directory Structure
```
Service Directory/
├── SftpFileUploaderService.exe
├── App.config
└── logs/
    ├── SftpFileUploader.log
    ├── SftpFileUploader.log.1
    ├── SftpFileUploader.log.2
    ├── SftpFileUploader_Errors.log
    └── SftpFileUploader_Errors.log.1
```

### Log File Naming
- **Main log**: `SftpFileUploader.log`
- **Error log**: `SftpFileUploader_Errors.log`
- **Backup files**: `.1`, `.2`, etc. (newest backup has lowest number)

## Configuration Customization

### Development Environment
```xml
<root>
  <level value="DEBUG" />
  <appender-ref ref="FileAppender" />
  <appender-ref ref="ErrorFileAppender" />
  <appender-ref ref="ConsoleAppender" />
  <!-- <appender-ref ref="EventLogAppender" /> -->
</root>
```

**Changes**:
- **DEBUG level**: More verbose logging
- **Console output**: Enabled for immediate feedback
- **Event log**: Disabled for development

### Production Environment
```xml
<root>
  <level value="INFO" />
  <appender-ref ref="FileAppender" />
  <appender-ref ref="ErrorFileAppender" />
  <appender-ref ref="EventLogAppender" />
</root>
```

**Changes**:
- **INFO level**: Balanced logging
- **No console**: File and event log only
- **Event log**: Enabled for monitoring

### High-Volume Environment
```xml
<appender name="FileAppender" type="log4net.Appender.RollingFileAppender">
  <file value="logs\SftpFileUploader.log" />
  <appendToFile value="true" />
  <rollingStyle value="Size" />
  <maxSizeRollBackups value="20" />
  <maximumFileSize value="50MB" />
  <staticLogFileName value="true" />
</appender>
```

**Changes**:
- **Larger files**: 50MB instead of 10MB
- **More backups**: 20 instead of 10
- **Higher capacity**: Handles more log volume

## Benefits of App.config Integration

### 1. Single Configuration File
- **Simplified deployment**: Only one config file to manage
- **Consistent location**: All settings in one place
- **Easier maintenance**: No separate log4net.config to track

### 2. Integrated Management
- **Configuration tools**: Can use standard .NET config tools
- **Transformation support**: Works with config transformations
- **Version control**: Single file to track changes

### 3. Service Integration
- **Automatic discovery**: log4net automatically finds config in app.config
- **No file paths**: No need to specify log4net.config location
- **Deployment simplicity**: Standard .NET application deployment

## Troubleshooting

### Common Issues

#### 1. Logs Not Created
**Symptoms**: No log files appear in logs directory
**Solutions**:
- Check if logs directory exists (create manually if needed)
- Verify service has write permissions to logs directory
- Check for configuration syntax errors

#### 2. Event Log Errors
**Symptoms**: Errors writing to Windows Event Log
**Solutions**:
- Run service with appropriate permissions
- Check if application name is registered in Event Log
- Verify EventLogAppender configuration

#### 3. Configuration Not Loading
**Symptoms**: Default logging behavior instead of configured
**Solutions**:
- Verify configSections declaration is correct
- Check XML syntax in log4net section
- Ensure XmlConfigurator.Configure() is called

### Validation Steps
1. **Check config syntax**: Validate XML structure
2. **Test permissions**: Verify write access to log directory
3. **Monitor startup**: Check for initialization errors
4. **Verify output**: Confirm logs appear in expected locations

## Performance Considerations

### File I/O Optimization
- **Buffered writing**: log4net buffers writes for performance
- **Asynchronous logging**: Consider AsyncAppender for high-volume scenarios
- **Log level filtering**: Use appropriate levels to reduce I/O

### Resource Management
- **File handles**: Rolling appenders manage file handles efficiently
- **Memory usage**: Pattern layouts are memory-efficient
- **Disk space**: Configure appropriate file sizes and backup counts
