# Retry Functionality Guide

## Overview
The FileUploaderService now includes comprehensive retry logic with 3 configurable retry attempts for all upload operations, providing robust error recovery and improved reliability.

## Retry Features

### 1. Configurable Retry Attempts
- **Default**: 3 retry attempts (4 total attempts including initial)
- **Configurable**: Can be set from 0 (no retries) to any reasonable number
- **Scope**: Applies to all upload operations

### 2. Exponential Backoff
- **Default**: Enabled with exponential backoff
- **Linear Option**: Can be disabled for fixed delay intervals
- **Formula**: Delay = InitialDelay × 2^(attempt-1)

### 3. Multi-Level Retry Protection
- **Connection Level**: SFTP connection establishment
- **Folder Level**: Complete folder upload operations
- **File Level**: Individual file upload operations

## Configuration

### Retry Settings
```xml
<!-- Maximum number of retry attempts (0 = no retries, 3 = 4 total attempts) -->
<add key="MaxRetryAttempts" value="3" />

<!-- Initial delay between retries in seconds -->
<add key="RetryDelaySeconds" value="5" />

<!-- Enable exponential backoff (delay doubles with each retry) -->
<add key="EnableRetryExponentialBackoff" value="true" />
```

### Example Configurations

#### Conservative (Slow but Reliable)
```xml
<add key="MaxRetryAttempts" value="5" />
<add key="RetryDelaySeconds" value="10" />
<add key="EnableRetryExponentialBackoff" value="true" />
```
**Timing**: 10s, 20s, 40s, 80s, 160s delays

#### Aggressive (Fast Recovery)
```xml
<add key="MaxRetryAttempts" value="2" />
<add key="RetryDelaySeconds" value="2" />
<add key="EnableRetryExponentialBackoff" value="false" />
```
**Timing**: 2s, 2s delays

#### Balanced (Recommended)
```xml
<add key="MaxRetryAttempts" value="3" />
<add key="RetryDelaySeconds" value="5" />
<add key="EnableRetryExponentialBackoff" value="true" />
```
**Timing**: 5s, 10s, 20s delays

## Retry Scope

### 1. SFTP Connection Retry
**What it covers:**
- Initial connection to SFTP server
- Authentication failures
- Network connectivity issues
- Server unavailability

**Example Log:**
```
INFO - Retry attempt 1/3 for SFTP connection and upload for C:\Data\Batch001
WARN - Attempt 1 failed for SFTP connection and upload for C:\Data\Batch001: Connection timeout. Retrying in 5 seconds...
INFO - Successfully connected to SFTP server: sftp.example.com
```

### 2. Folder Upload Retry
**What it covers:**
- Complete folder upload operations
- Directory creation failures
- Session-level errors

**Example Log:**
```
INFO - Retry attempt 2/3 for folder upload for C:\Data\Batch001
WARN - Attempt 2 failed for folder upload for C:\Data\Batch001: Session lost. Retrying in 10 seconds...
INFO - Successfully completed folder upload for C:\Data\Batch001
```

### 3. Individual File Upload Retry
**What it covers:**
- Single file transfer failures
- File access issues
- Temporary network glitches

**Example Log:**
```
INFO - Retry attempt 1/3 for file upload for document.pdf
WARN - Attempt 1 failed for file upload for document.pdf: Transfer interrupted. Retrying in 5 seconds...
INFO - Successfully uploaded: C:\Data\document.pdf -> /upload/document.pdf
```

## Error Scenarios Handled

### Network Issues
- **Connection timeouts**
- **Intermittent connectivity**
- **DNS resolution failures**
- **Firewall blocking**

### Server Issues
- **Temporary server unavailability**
- **Authentication token expiration**
- **Server overload/rate limiting**
- **Maintenance windows**

### File System Issues
- **Temporary file locks**
- **Disk space issues**
- **Permission changes**
- **Network drive disconnections**

## Retry Logic Flow

### 1. Initial Attempt
```
Operation starts → Success? → Continue
                ↓ Failure
                Check retry count
```

### 2. Retry Decision
```
Attempts < MaxRetries? → Yes → Calculate delay → Wait → Retry
                      ↓ No
                      Log final failure → Throw exception
```

### 3. Delay Calculation
```
Exponential Backoff: delay = initialDelay × 2^(attempt-1)
Linear Backoff:      delay = initialDelay
```

## Monitoring and Logging

### Success Indicators
```
INFO - Successfully connected to SFTP server: [hostname]
INFO - Successfully uploaded: [local] -> [remote]
INFO - Successfully completed folder upload for [path]
```

### Retry Indicators
```
INFO - Retry attempt [X]/[Y] for [operation]
WARN - Attempt [X] failed for [operation]: [error]. Retrying in [N] seconds...
```

### Failure Indicators
```
ERROR - All [N] attempts failed for [operation]: [final error]
ERROR - Error uploading folder [path] after all retry attempts: [error]
```

## Performance Impact

### Benefits
- **Improved Reliability**: Handles transient failures automatically
- **Reduced Manual Intervention**: Automatic recovery from temporary issues
- **Better Success Rates**: Multiple attempts increase upload success probability

### Considerations
- **Increased Processing Time**: Failed operations take longer due to retries
- **Resource Usage**: Additional CPU and memory during retry operations
- **Log Volume**: More detailed logging during retry scenarios

### Optimization Tips
1. **Tune Retry Count**: Balance reliability vs. performance
2. **Adjust Delays**: Shorter delays for faster recovery, longer for server issues
3. **Monitor Logs**: Identify patterns in failures to optimize settings
4. **Network Quality**: Better networks may need fewer retries

## Troubleshooting

### High Retry Rates
**Symptoms**: Many retry attempts in logs
**Possible Causes**:
- Network instability
- Server overload
- Authentication issues
- Firewall problems

**Solutions**:
- Check network connectivity
- Verify SFTP server status
- Review authentication credentials
- Adjust retry settings

### Retry Exhaustion
**Symptoms**: "All attempts failed" messages
**Possible Causes**:
- Persistent server issues
- Configuration problems
- File system issues

**Solutions**:
- Increase retry count
- Increase retry delays
- Check server logs
- Verify file permissions

### Performance Issues
**Symptoms**: Slow upload operations
**Possible Causes**:
- Too many retries
- Long retry delays
- Frequent failures

**Solutions**:
- Reduce retry count
- Disable exponential backoff
- Fix underlying issues
- Optimize network

## Best Practices

### Configuration
1. **Start Conservative**: Begin with default settings
2. **Monitor Performance**: Track retry rates and success
3. **Adjust Gradually**: Make incremental changes
4. **Test Thoroughly**: Verify settings in your environment

### Monitoring
1. **Watch Retry Logs**: Monitor for patterns
2. **Track Success Rates**: Measure improvement
3. **Alert on Failures**: Set up alerts for retry exhaustion
4. **Regular Review**: Periodically review and optimize settings

### Maintenance
1. **Log Rotation**: Ensure adequate log space
2. **Performance Testing**: Regular testing under load
3. **Configuration Backup**: Keep retry settings documented
4. **Update Documentation**: Keep settings current
