<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- SFTP Connection Settings -->
    <add key="FtpHost" value="your-sftp-server.com" />
    <add key="FtpUsername" value="your-username" />
    <add key="FtpPassword" value="your-password" />
    <add key="FtpRemotePath" value="/upload/" />
    <add key="SshFingerprint" value="ssh-rsa 2048 xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx" />
    
    <!-- Local Folder Settings -->
    <add key="WatchFolder" value="C:\path\to\watch\folder" />
    
    <!-- Upload Behavior Settings -->
    <add key="MaxConcurrentUploads" value="5" />
    <add key="WaitMinutesBeforeUpload" value="2" />
    
    <!-- NEW: Folder Upload Settings -->
    <!-- File patterns to include (semicolon or comma separated) -->
    <add key="IncludeFilePatterns" value="*.pdf;*.txt;*.xml;*.csv;*.xlsx" />
    
    <!-- File patterns to exclude (semicolon or comma separated) -->
    <add key="ExcludeFilePatterns" value="*.Rdy;*.tmp;*.log;*.bak;*~" />
    
    <!-- Whether to delete the trigger file after successful upload -->
    <add key="DeleteTriggerFileAfterUpload" value="true" />
    
    <!-- Whether to preserve folder structure on remote server -->
    <add key="PreserveFolderStructure" value="false" />

    <!-- XML Parsing Settings for *.Rdy files -->
    <!-- Element name containing the folder path (leave empty to use attribute) -->
    <add key="XmlFolderPathElement" value="FolderPath" />

    <!-- Attribute name containing the folder path (leave empty to use element) -->
    <add key="XmlFolderPathAttribute" value="" />

    <!-- Directory Creation Settings -->
    <!-- Whether to pre-create all directories before starting uploads -->
    <add key="PreCreateDirectories" value="true" />

    <!-- Whether to create directories recursively (parent directories first) -->
    <add key="CreateDirectoriesRecursively" value="true" />

    <!-- File Detection Settings -->
    <!-- Enable polling in addition to FileSystemWatcher (useful for network drives) -->
    <add key="EnablePolling" value="false" />

    <!-- Polling interval in seconds (only used if EnablePolling is true) -->
    <add key="PollingIntervalSeconds" value="30" />

    <!-- Enable detailed logging for file detection debugging -->
    <add key="EnableDetailedLogging" value="false" />

    <!-- Retry Settings -->
    <!-- Maximum number of retry attempts (0 = no retries, 3 = 4 total attempts) -->
    <add key="MaxRetryAttempts" value="3" />

    <!-- Initial delay between retries in seconds -->
    <add key="RetryDelaySeconds" value="5" />

    <!-- Enable exponential backoff (delay doubles with each retry) -->
    <add key="EnableRetryExponentialBackoff" value="true" />

    <!-- Failure Handling Settings -->
    <!-- Stop processing new uploads after retry exhaustion (recommended: false) -->
    <add key="StopProcessingOnRetryExhaustion" value="false" />

    <!-- Stop the entire service on critical failures (recommended: false) -->
    <add key="StopServiceOnCriticalFailure" value="false" />

    <!-- Maximum consecutive failures before triggering failure handling -->
    <add key="MaxConsecutiveFailures" value="5" />
  </appSettings>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
</configuration>
