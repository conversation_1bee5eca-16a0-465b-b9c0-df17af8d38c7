<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
  </configSections>

  <appSettings>
    <!-- SFTP Connection Settings -->
    <add key="FtpHost" value="your-sftp-server.com" />
    <add key="FtpUsername" value="your-username" />
    <add key="FtpPassword" value="your-password" />
    <add key="FtpRemotePath" value="/upload/" />
    <add key="SshFingerprint" value="ssh-rsa 2048 xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx" />
    
    <!-- Local Folder Settings -->
    <add key="WatchFolder" value="C:\path\to\watch\folder" />
    
    <!-- Upload Behavior Settings -->
    <add key="MaxConcurrentUploads" value="5" />
    <add key="WaitMinutesBeforeUpload" value="2" />
    
    <!-- NEW: Folder Upload Settings -->
    <!-- File patterns to include (semicolon or comma separated) -->
    <add key="IncludeFilePatterns" value="*.pdf;*.txt;*.xml;*.csv;*.xlsx" />
    
    <!-- File patterns to exclude (semicolon or comma separated) -->
    <add key="ExcludeFilePatterns" value="*.Rdy;*.tmp;*.log;*.bak;*~" />
    
    <!-- Whether to delete the trigger file after successful upload -->
    <add key="DeleteTriggerFileAfterUpload" value="true" />
    
    <!-- Whether to preserve folder structure on remote server -->
    <add key="PreserveFolderStructure" value="false" />

    <!-- XML Parsing Settings for *.Rdy files -->
    <!-- Element name containing the folder path (leave empty to use attribute) -->
    <add key="XmlFolderPathElement" value="FolderPath" />

    <!-- Attribute name containing the folder path (leave empty to use element) -->
    <add key="XmlFolderPathAttribute" value="" />

    <!-- Directory Creation Settings -->
    <!-- Whether to pre-create all directories before starting uploads -->
    <add key="PreCreateDirectories" value="true" />

    <!-- Whether to create directories recursively (parent directories first) -->
    <add key="CreateDirectoriesRecursively" value="true" />

    <!-- File Detection Settings -->
    <!-- Enable polling in addition to FileSystemWatcher (useful for network drives) -->
    <add key="EnablePolling" value="false" />

    <!-- Polling interval in seconds (only used if EnablePolling is true) -->
    <add key="PollingIntervalSeconds" value="30" />

    <!-- Enable detailed logging for file detection debugging -->
    <add key="EnableDetailedLogging" value="false" />

    <!-- Retry Settings -->
    <!-- Maximum number of retry attempts (0 = no retries, 3 = 4 total attempts) -->
    <add key="MaxRetryAttempts" value="3" />

    <!-- Initial delay between retries in seconds -->
    <add key="RetryDelaySeconds" value="5" />

    <!-- Enable exponential backoff (delay doubles with each retry) -->
    <add key="EnableRetryExponentialBackoff" value="true" />

    <!-- Failure Handling Settings -->
    <!-- Stop processing new uploads after retry exhaustion (recommended: false) -->
    <add key="StopProcessingOnRetryExhaustion" value="false" />

    <!-- Stop the entire service on critical failures (recommended: false) -->
    <add key="StopServiceOnCriticalFailure" value="false" />

    <!-- Maximum consecutive failures before triggering failure handling -->
    <add key="MaxConsecutiveFailures" value="5" />

    <!-- Queue Management Settings -->
    <!-- Remove queued uploads when trigger file is deleted (recommended: true) -->
    <add key="RemoveFromQueueOnTriggerFileDeleted" value="true" />

    <!-- Database Logging Settings -->
    <!-- Enable database logging for process tracking (recommended: false for initial setup) -->
    <add key="EnableDatabaseLogging" value="false" />

    <!-- Database connection string (SQL Server) -->
    <add key="DatabaseConnectionString" value="Server=localhost;Database=FileUploaderDB;Integrated Security=true;" />

    <!-- Database command timeout in seconds -->
    <add key="DatabaseTimeoutSeconds" value="30" />

    <!-- Log service start/stop events to database -->
    <add key="LogServiceEvents" value="true" />

    <!-- Log upload start/end events to database -->
    <add key="LogUploadEvents" value="true" />

    <!-- Log individual file upload events to database (can be verbose) -->
    <add key="LogFileEvents" value="false" />

    <!-- File Locking Settings -->
    <!-- Enable trigger file locking during upload process (recommended: true) -->
    <add key="EnableTriggerFileLocking" value="true" />

    <!-- File lock timeout in seconds (default: 30) -->
    <add key="FileLockTimeoutSeconds" value="30" />
  </appSettings>

  <log4net>
    <!-- File Appender for General Logs -->
    <appender name="FileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs\SftpFileUploader.log" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="10MB" />
      <staticLogFileName value="true" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
      </layout>
    </appender>

    <!-- File Appender for Error Logs -->
    <appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs\SftpFileUploader_Errors.log" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="5" />
      <maximumFileSize value="5MB" />
      <staticLogFileName value="true" />
      <threshold value="WARN" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger - %message%newline%exception" />
      </layout>
    </appender>

    <!-- Console Appender for Development -->
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline" />
      </layout>
    </appender>

    <!-- Event Log Appender for Windows Service -->
    <appender name="EventLogAppender" type="log4net.Appender.EventLogAppender">
      <logName value="Application" />
      <applicationName value="SftpFileUploaderService" />
      <threshold value="WARN" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level %logger - %message" />
      </layout>
    </appender>

    <!-- Root Logger Configuration -->
    <root>
      <level value="INFO" />
      <appender-ref ref="FileAppender" />
      <appender-ref ref="ErrorFileAppender" />
      <appender-ref ref="EventLogAppender" />
      <!-- Uncomment for development/debugging -->
      <!-- <appender-ref ref="ConsoleAppender" /> -->
    </root>

    <!-- Specific Logger for Upload Operations -->
    <logger name="SftpFileUploaderService.FileUploaderService" additivity="false">
      <level value="DEBUG" />
      <appender-ref ref="FileAppender" />
      <appender-ref ref="ErrorFileAppender" />
      <appender-ref ref="EventLogAppender" />
    </logger>
  </log4net>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
</configuration>
