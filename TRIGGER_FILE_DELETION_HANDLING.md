# Trigger File Deletion Handling

## Overview
The FileUploaderService now automatically removes queued uploads from the processing queue when their corresponding trigger files are deleted. This prevents unnecessary processing attempts and provides better queue management.

## Feature Description

### Problem Addressed
**Before**: If a trigger file was deleted after being detected but before processing, the service would still attempt to upload the folder, potentially causing errors or processing files that were no longer intended for upload.

**After**: The service monitors for trigger file deletions and automatically removes corresponding queue items, preventing unnecessary processing.

## Implementation Details

### 1. FileSystemWatcher Enhancement
**Added Deleted Event Monitoring**:
```csharp
// Set up event handlers
watcher.Created += OnFileCreated;
watcher.Changed += OnFileChanged;
watcher.Deleted += OnFileDeleted;  // NEW: Monitor deletions
watcher.Error += OnWatcherError;
```

### 2. Deletion Event Handler
```csharp
private void OnFileDeleted(object sender, FileSystemEventArgs e)
{
    if (enableDetailedLogging)
    {
        LogDebug($"FileSystemWatcher.Deleted event: {e.FullPath}");
    }
    
    if (removeFromQueueOnTriggerFileDeleted)
    {
        ProcessTriggerFileDeleted(e.FullPath);
    }
}
```

### 3. Queue Management Strategy
Since `ConcurrentQueue<T>` doesn't support direct item removal, the implementation uses a tracking approach:

**Deleted Files Tracking**:
```csharp
private readonly HashSet<string> deletedTriggerFiles = new HashSet<string>();
private readonly object deletedTriggerFilesLock = new object();
```

**Marking for Removal**:
```csharp
private void ProcessTriggerFileDeleted(string deletedFilePath)
{
    // Add to deleted files tracking
    lock (deletedTriggerFilesLock)
    {
        deletedTriggerFiles.Add(deletedFilePath);
    }

    // Count and log affected queue items
    int markedForRemoval = CountQueueItemsForTriggerFile(deletedFilePath);
    
    if (markedForRemoval > 0)
    {
        LogInfo($"Marked {markedForRemoval} queue item(s) for removal due to deleted trigger file: {deletedFilePath}");
    }
}
```

### 4. Queue Processing Enhancement
**Skip Deleted Items During Processing**:
```csharp
if (uploadQueue.TryPeek(out FolderUploadInfo folderInfo))
{
    // Check if the trigger file has been deleted
    bool triggerFileDeleted = false;
    lock (deletedTriggerFilesLock)
    {
        triggerFileDeleted = deletedTriggerFiles.Contains(folderInfo.TriggerFilePath);
    }

    if (triggerFileDeleted)
    {
        // Remove from queue and skip processing
        if (uploadQueue.TryDequeue(out folderInfo))
        {
            LogInfo($"Skipping upload for {folderInfo.FolderPath} - trigger file was deleted: {folderInfo.TriggerFilePath}");
        }
        continue; // Check next item in queue
    }
    
    // Continue with normal processing...
}
```

### 5. Cleanup Mechanism
**Automatic Cleanup After Processing**:
```csharp
// Clean up deleted trigger files tracking for this completed upload
lock (deletedTriggerFilesLock)
{
    if (deletedTriggerFiles.Remove(folderInfo.TriggerFilePath))
    {
        if (enableDetailedLogging)
        {
            LogDebug($"Removed {folderInfo.TriggerFilePath} from deleted trigger files tracking");
        }
    }
}
```

## Configuration

### New Configuration Option
```xml
<!-- Queue Management Settings -->
<!-- Remove queued uploads when trigger file is deleted (recommended: true) -->
<add key="RemoveFromQueueOnTriggerFileDeleted" value="true" />
```

### Configuration Options
- **`true` (recommended)**: Automatically remove queue items when trigger files are deleted
- **`false`**: Continue processing even if trigger files are deleted (legacy behavior)

## Logging Examples

### Trigger File Deletion Detection
```
INFO - Trigger file deleted: C:\Upload\batch001.Rdy. Checking queue for removal.
INFO - Marked 1 queue item(s) for removal due to deleted trigger file: C:\Upload\batch001.Rdy
```

### Queue Item Removal
```
INFO - Skipping upload for C:\Data\Batch001 - trigger file was deleted: C:\Upload\batch001.Rdy
```

### Detailed Logging (when enabled)
```
DEBUG - FileSystemWatcher.Deleted event: C:\Upload\batch001.Rdy
DEBUG - No queue items found for deleted trigger file: C:\Upload\batch002.Rdy
DEBUG - Removed C:\Upload\batch001.Rdy from deleted trigger files tracking
DEBUG - Cleared deleted trigger files cache
```

## Operational Benefits

### 1. Prevents Unnecessary Processing
- **Resource savings**: Avoids processing folders when trigger files are removed
- **Error reduction**: Prevents attempts to upload files that may no longer be intended for upload
- **Queue efficiency**: Keeps queue focused on valid upload requests

### 2. Responsive Queue Management
- **Real-time removal**: Immediately responds to trigger file deletions
- **Automatic cleanup**: No manual intervention required
- **Memory efficient**: Automatic cleanup of tracking data

### 3. Flexible Configuration
- **Opt-in behavior**: Can be disabled if legacy behavior is preferred
- **Backward compatible**: Default behavior maintains existing functionality
- **Environment specific**: Can be configured differently per environment

## Use Cases

### 1. Manual Intervention
**Scenario**: Administrator realizes a batch should not be processed
**Action**: Delete the trigger file
**Result**: Upload is automatically removed from queue

### 2. Application Error Recovery
**Scenario**: Application creates trigger file by mistake
**Action**: Application deletes the erroneous trigger file
**Result**: No unnecessary upload processing occurs

### 3. Batch Cancellation
**Scenario**: Business process determines a batch should be cancelled
**Action**: Automated system deletes trigger file
**Result**: Upload is cancelled without manual intervention

### 4. File System Cleanup
**Scenario**: Maintenance scripts clean up old trigger files
**Action**: Cleanup script removes outdated trigger files
**Result**: Corresponding uploads are automatically removed from queue

## Performance Considerations

### Memory Usage
- **Tracking overhead**: Small memory footprint for deleted files tracking
- **Automatic cleanup**: Periodic cleanup prevents unlimited growth
- **Efficient lookups**: HashSet provides O(1) lookup performance

### Processing Impact
- **Minimal overhead**: Quick check during queue processing
- **Thread-safe operations**: Lock-based synchronization for safety
- **No blocking**: Non-blocking queue operations

### Scalability
- **High-volume support**: Efficient for environments with many trigger files
- **Cleanup strategy**: Automatic cleanup prevents memory leaks
- **Configurable behavior**: Can be disabled in high-performance scenarios

## Thread Safety

### Synchronization Strategy
- **Lock-based protection**: Thread-safe access to deleted files tracking
- **Atomic operations**: Safe concurrent access to shared data structures
- **Race condition prevention**: Proper ordering of operations

### Concurrent Access
- **Multiple threads**: Safe access from FileSystemWatcher and queue processing threads
- **Lock granularity**: Fine-grained locking for optimal performance
- **Deadlock prevention**: Simple lock ordering prevents deadlocks

## Troubleshooting

### Common Scenarios

#### 1. Files Not Being Removed from Queue
**Symptoms**: Queue items still processed after trigger file deletion
**Possible Causes**:
- Feature disabled in configuration
- FileSystemWatcher not detecting deletions
- Timing issues with file deletion

**Solutions**:
- Verify `RemoveFromQueueOnTriggerFileDeleted=true`
- Check FileSystemWatcher is working properly
- Enable detailed logging to see deletion events

#### 2. Memory Usage Growth
**Symptoms**: Increasing memory usage over time
**Possible Causes**:
- Deleted files tracking not being cleaned up
- High volume of deletions without cleanup

**Solutions**:
- Monitor cleanup log messages
- Restart service periodically if needed
- Adjust cleanup thresholds if necessary

#### 3. Performance Impact
**Symptoms**: Slower queue processing
**Possible Causes**:
- Lock contention on deleted files tracking
- Large number of tracked deleted files

**Solutions**:
- Monitor lock contention
- Increase cleanup frequency
- Consider disabling feature in high-performance scenarios

### Diagnostic Steps
1. **Enable detailed logging**: Set `EnableDetailedLogging=true`
2. **Monitor deletion events**: Look for "FileSystemWatcher.Deleted event" messages
3. **Check queue removal**: Look for "Skipping upload" messages
4. **Verify cleanup**: Look for cleanup log messages

## Best Practices

### Configuration
1. **Enable by default**: Recommended for most environments
2. **Monitor behavior**: Watch logs to ensure proper operation
3. **Test thoroughly**: Verify behavior in your specific environment

### Operations
1. **Coordinate deletions**: Ensure trigger file deletions are intentional
2. **Monitor queue size**: Watch for unexpected queue growth or shrinkage
3. **Regular maintenance**: Monitor memory usage and cleanup effectiveness

### Development
1. **Handle edge cases**: Consider race conditions between detection and deletion
2. **Test scenarios**: Test various deletion timing scenarios
3. **Error handling**: Ensure robust error handling for deletion processing

This feature provides intelligent queue management that responds to trigger file deletions, improving the overall reliability and efficiency of the file upload service.
