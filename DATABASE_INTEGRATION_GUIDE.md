# Database Integration Guide

## Overview
The FileUploaderService now includes comprehensive database logging capabilities to track service lifecycle events, upload processes, and individual file operations. This provides detailed audit trails and operational monitoring capabilities.

## Database Features

### 1. Service Event Logging
**Tracks service lifecycle events:**
- Service start/stop events
- Configuration details
- Service uptime tracking
- Machine and service identification

### 2. Upload Process Logging
**Tracks upload operations:**
- Upload start/end events
- File counts and total sizes
- Success/failure status
- Error details for failed uploads
- Trigger file and folder path tracking

### 3. File Event Logging (Optional)
**Tracks individual file operations:**
- Individual file upload events
- File sizes and paths
- Remote destination paths
- File-level error tracking

## Database Schema

### Tables Created

#### ServiceEvents Table
```sql
CREATE TABLE ServiceEvents (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    EventTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    EventType NVARCHAR(50) NOT NULL,
    Description NVARCHAR(500) NOT NULL,
    Details NVARCHAR(MAX) NULL,
    MachineName NVARCHAR(100) NOT NULL,
    ServiceName NVARCHAR(100) NOT NULL
);
```

**Event Types:**
- `SERVICE_START`: Service startup
- `SERVICE_STOP`: Service shutdown

#### UploadEvents Table
```sql
CREATE TABLE UploadEvents (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    EventTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    EventType NVARCHAR(50) NOT NULL,
    TriggerFilePath NVARCHAR(500) NOT NULL,
    FolderPath NVARCHAR(500) NOT NULL,
    FileCount INT NULL,
    TotalSizeBytes BIGINT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    MachineName NVARCHAR(100) NOT NULL,
    ServiceName NVARCHAR(100) NOT NULL
);
```

**Event Types:**
- `UPLOAD_START`: Upload process begins
- `UPLOAD_SUCCESS`: Upload completed successfully
- `UPLOAD_FAILED`: Upload failed after all retries
- `UPLOAD_EMPTY`: No files found to upload

#### FileEvents Table (Optional)
```sql
CREATE TABLE FileEvents (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    EventTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    EventType NVARCHAR(50) NOT NULL,
    LocalFilePath NVARCHAR(500) NOT NULL,
    RemoteFilePath NVARCHAR(500) NULL,
    FileSizeBytes BIGINT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    MachineName NVARCHAR(100) NOT NULL,
    ServiceName NVARCHAR(100) NOT NULL
);
```

**Event Types:**
- `FILE_UPLOAD_START`: Individual file upload begins
- `FILE_UPLOAD_SUCCESS`: Individual file uploaded successfully
- `FILE_UPLOAD_FAILED`: Individual file upload failed

## Configuration

### Database Settings
```xml
<!-- Database Logging Settings -->
<!-- Enable database logging for process tracking (recommended: false for initial setup) -->
<add key="EnableDatabaseLogging" value="false" />

<!-- Database connection string (SQL Server) -->
<add key="DatabaseConnectionString" value="Server=localhost;Database=FileUploaderDB;Integrated Security=true;" />

<!-- Database command timeout in seconds -->
<add key="DatabaseTimeoutSeconds" value="30" />

<!-- Log service start/stop events to database -->
<add key="LogServiceEvents" value="true" />

<!-- Log upload start/end events to database -->
<add key="LogUploadEvents" value="true" />

<!-- Log individual file upload events to database (can be verbose) -->
<add key="LogFileEvents" value="false" />
```

### Configuration Options

#### EnableDatabaseLogging
- **`true`**: Enable all database logging features
- **`false`**: Disable database logging (default for initial setup)

#### DatabaseConnectionString
- **SQL Server**: `Server=localhost;Database=FileUploaderDB;Integrated Security=true;`
- **SQL Server with credentials**: `Server=localhost;Database=FileUploaderDB;User Id=username;Password=password;`
- **SQL Server Express**: `Server=localhost\SQLEXPRESS;Database=FileUploaderDB;Integrated Security=true;`

#### LogServiceEvents
- **`true`**: Log service start/stop events (recommended)
- **`false`**: Skip service event logging

#### LogUploadEvents  
- **`true`**: Log upload process events (recommended)
- **`false`**: Skip upload event logging

#### LogFileEvents
- **`true`**: Log individual file events (verbose, use with caution)
- **`false`**: Skip file-level logging (recommended for most scenarios)

## Setup Instructions

### 1. Database Setup
```sql
-- Create database
CREATE DATABASE FileUploaderDB;
GO

-- Run the schema script
-- Execute DatabaseSchema.sql to create tables, views, and procedures
```

### 2. Service Configuration
```xml
<!-- Enable database logging -->
<add key="EnableDatabaseLogging" value="true" />

<!-- Configure connection string -->
<add key="DatabaseConnectionString" value="Server=YourServer;Database=FileUploaderDB;Integrated Security=true;" />
```

### 3. Permissions Setup
```sql
-- Create service user (optional)
CREATE LOGIN [DOMAIN\ServiceAccount] FROM WINDOWS;
CREATE USER [FileUploaderServiceUser] FOR LOGIN [DOMAIN\ServiceAccount];

-- Grant necessary permissions
GRANT SELECT, INSERT ON ServiceEvents TO [FileUploaderServiceUser];
GRANT SELECT, INSERT ON UploadEvents TO [FileUploaderServiceUser];
GRANT SELECT, INSERT ON FileEvents TO [FileUploaderServiceUser];
```

## Logging Examples

### Service Events
```sql
-- Service start event
INSERT INTO ServiceEvents (EventType, Description, Details, MachineName, ServiceName)
VALUES ('SERVICE_START', 'File uploader service started successfully', 
        'WatchFolder: C:\Upload, MaxConcurrentUploads: 5', 'SERVER01', 'SftpFileUploaderService');

-- Service stop event  
INSERT INTO ServiceEvents (EventType, Description, Details, MachineName, ServiceName)
VALUES ('SERVICE_STOP', 'File uploader service stopping', 
        'Uptime: 02.14:35:22', 'SERVER01', 'SftpFileUploaderService');
```

### Upload Events
```sql
-- Upload start
INSERT INTO UploadEvents (EventType, TriggerFilePath, FolderPath, FileCount, TotalSizeBytes, MachineName, ServiceName)
VALUES ('UPLOAD_START', 'C:\Upload\batch001.Rdy', 'C:\Data\Batch001', 25, 1048576000, 'SERVER01', 'SftpFileUploaderService');

-- Upload success
INSERT INTO UploadEvents (EventType, TriggerFilePath, FolderPath, FileCount, TotalSizeBytes, MachineName, ServiceName)
VALUES ('UPLOAD_SUCCESS', 'C:\Upload\batch001.Rdy', 'C:\Data\Batch001', 25, 1048576000, 'SERVER01', 'SftpFileUploaderService');

-- Upload failure
INSERT INTO UploadEvents (EventType, TriggerFilePath, FolderPath, ErrorMessage, MachineName, ServiceName)
VALUES ('UPLOAD_FAILED', 'C:\Upload\batch002.Rdy', 'C:\Data\Batch002', 'Connection timeout after all retry attempts', 'SERVER01', 'SftpFileUploaderService');
```

## Monitoring and Reporting

### Built-in Views

#### Service Status View
```sql
SELECT * FROM vw_ServiceStatus;
```
**Returns:**
- Current service status (RUNNING/STOPPED)
- Last start/stop times
- Machine and service names

#### Upload Summary View
```sql
SELECT * FROM vw_UploadSummary WHERE UploadDate >= DATEADD(DAY, -7, GETDATE());
```
**Returns:**
- Daily upload statistics
- Success/failure counts
- Total files and bytes uploaded

#### Recent Upload Activity View
```sql
SELECT * FROM vw_RecentUploadActivity;
```
**Returns:**
- Last 100 upload events
- Event details and timestamps

### Built-in Stored Procedures

#### Get Service Status
```sql
EXEC sp_GetServiceStatus @MachineName = 'SERVER01';
```

#### Get Upload Statistics
```sql
EXEC sp_GetUploadStatistics 
    @StartDate = '2024-01-01', 
    @EndDate = '2024-01-31',
    @MachineName = 'SERVER01';
```

#### Get Failed Uploads
```sql
EXEC sp_GetFailedUploads 
    @StartDate = DATEADD(HOUR, -24, GETDATE()),
    @MachineName = 'SERVER01';
```

#### Cleanup Old Records
```sql
EXEC sp_CleanupOldRecords @RetentionDays = 90;
```

## Performance Considerations

### Database Impact
- **Minimal overhead**: Asynchronous logging doesn't block upload operations
- **Indexed tables**: Optimized for common query patterns
- **Configurable verbosity**: Control logging level based on needs

### Storage Requirements
- **ServiceEvents**: ~200 bytes per event
- **UploadEvents**: ~500 bytes per event  
- **FileEvents**: ~300 bytes per file (can be high volume)

### Recommended Settings
```xml
<!-- Production environment -->
<add key="LogServiceEvents" value="true" />
<add key="LogUploadEvents" value="true" />
<add key="LogFileEvents" value="false" />

<!-- Development environment -->
<add key="LogServiceEvents" value="true" />
<add key="LogUploadEvents" value="true" />
<add key="LogFileEvents" value="true" />
```

## Troubleshooting

### Common Issues

#### Database Connection Failures
**Symptoms**: Service starts but no database records appear
**Solutions:**
- Verify connection string
- Check database permissions
- Ensure SQL Server is accessible
- Review service logs for database errors

#### Performance Impact
**Symptoms**: Slower upload operations
**Solutions:**
- Disable FileEvents logging
- Optimize database server
- Check network connectivity to database
- Consider asynchronous logging improvements

#### Storage Growth
**Symptoms**: Database growing rapidly
**Solutions:**
- Disable FileEvents logging
- Implement regular cleanup procedures
- Archive old records
- Monitor table sizes

### Diagnostic Queries

#### Check Recent Database Activity
```sql
SELECT TOP 10 * FROM ServiceEvents ORDER BY EventTime DESC;
SELECT TOP 10 * FROM UploadEvents ORDER BY EventTime DESC;
```

#### Monitor Database Performance
```sql
-- Check table sizes
SELECT 
    t.name AS TableName,
    p.rows AS RowCount,
    (a.total_pages * 8) / 1024 AS TotalSpaceMB
FROM sys.tables t
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE t.name IN ('ServiceEvents', 'UploadEvents', 'FileEvents')
AND i.index_id <= 1
GROUP BY t.name, p.rows, a.total_pages;
```

## Maintenance

### Regular Cleanup
```sql
-- Schedule this to run weekly/monthly
EXEC sp_CleanupOldRecords @RetentionDays = 90;
```

### Index Maintenance
```sql
-- Rebuild indexes monthly
ALTER INDEX ALL ON ServiceEvents REBUILD;
ALTER INDEX ALL ON UploadEvents REBUILD;
ALTER INDEX ALL ON FileEvents REBUILD;
```

### Backup Considerations
- Include database in regular backup procedures
- Consider separate backup schedule for audit data
- Test restore procedures regularly

This database integration provides comprehensive tracking and monitoring capabilities for the FileUploaderService, enabling detailed operational insights and audit trails.
