# File Detection Troubleshooting Guide

## Overview
This guide helps troubleshoot issues when the FileUploaderService is not detecting new *.Rdy files.

## Enhanced File Detection Features

### 1. Dual Detection Methods
- **FileSystemWatcher**: Real-time file system events (primary method)
- **Polling**: Periodic folder scanning (backup method for problematic scenarios)

### 2. Comprehensive Diagnostics
- **Folder Validation**: Checks if watch folder exists and is accessible
- **Permission Testing**: Verifies write permissions to watch folder
- **Enhanced Logging**: Detailed logging for debugging file detection issues
- **Error Recovery**: Automatic FileSystemWatcher restart on errors

## Common Issues and Solutions

### Issue 1: Service Starts But No Files Detected

**Symptoms:**
- Service logs show successful startup
- No "Detected trigger file" messages in logs
- Files are being created but not processed

**Troubleshooting Steps:**

1. **Check Watch Folder Configuration**
   ```xml
   <add key="WatchFolder" value="C:\YourActualPath\WatchFolder" />
   ```
   - Verify the path exists
   - Check for typos in the path
   - Ensure path uses correct slashes (backslashes for Windows)

2. **Enable Detailed Logging**
   ```xml
   <add key="EnableDetailedLogging" value="true" />
   ```
   - Restart service
   - Check logs for detailed file detection events

3. **Check Service Logs**
   Look for these messages in logs:
   - `"Watch folder validation successful: [path]"`
   - `"FileSystemWatcher configured for: [path]"`
   - `"Service started successfully"`

### Issue 2: Network Drive or UNC Path Issues

**Symptoms:**
- Watch folder is on network drive (\\server\share\folder)
- FileSystemWatcher doesn't work reliably on network paths

**Solution: Enable Polling**
```xml
<add key="EnablePolling" value="true" />
<add key="PollingIntervalSeconds" value="30" />
```

### Issue 3: Permission Issues

**Symptoms:**
- Service starts but logs show permission warnings
- Files created by other users/processes not detected

**Solutions:**
1. **Run service with appropriate permissions**
2. **Check folder permissions**
3. **Look for this log message:**
   `"Watch folder may have permission issues"`

### Issue 4: File Creation Method Issues

**Symptoms:**
- Files created by certain applications not detected
- Files appear but no events triggered

**Common Causes:**
- Applications that create files via rename operations
- Files created with special attributes
- Very fast file creation/deletion

**Solution: Enable Polling as Backup**
```xml
<add key="EnablePolling" value="true" />
<add key="PollingIntervalSeconds" value="15" />
```

### Issue 5: Buffer Overflow

**Symptoms:**
- Many files created quickly
- Some files not detected
- FileSystemWatcher error messages

**Solution: Already Enhanced**
- Service now uses larger buffer size (32KB vs default 8KB)
- Automatic watcher restart on errors

## Configuration for Different Scenarios

### Scenario 1: Local Folder (Recommended)
```xml
<add key="WatchFolder" value="C:\Upload\TriggerFiles" />
<add key="EnablePolling" value="false" />
<add key="EnableDetailedLogging" value="false" />
```

### Scenario 2: Network Drive
```xml
<add key="WatchFolder" value="\\server\share\upload" />
<add key="EnablePolling" value="true" />
<add key="PollingIntervalSeconds" value="30" />
<add key="EnableDetailedLogging" value="true" />
```

### Scenario 3: High-Volume Environment
```xml
<add key="EnablePolling" value="true" />
<add key="PollingIntervalSeconds" value="10" />
<add key="EnableDetailedLogging" value="false" />
```

### Scenario 4: Debugging Issues
```xml
<add key="EnablePolling" value="true" />
<add key="PollingIntervalSeconds" value="15" />
<add key="EnableDetailedLogging" value="true" />
```

## Diagnostic Log Messages

### Successful Detection
```
INFO - Service started successfully. Monitoring folder: C:\Upload for *.Rdy trigger files
INFO - File detection methods: FileSystemWatcher=True, Polling=True
INFO - Detected trigger file via FileSystemWatcher.Created: C:\Upload\batch001.Rdy
INFO - Parsed folder path from XML: C:\Data\Batch001
```

### Error Indicators
```
ERROR - Watch folder does not exist: C:\Upload
ERROR - FileSystemWatcher error: [error details]
WARN - Watch folder may have permission issues
ERROR - Could not parse folder path from XML file: [file path]
```

## Testing File Detection

### Manual Test Steps

1. **Create Test File**
   ```xml
   <?xml version="1.0" encoding="utf-8"?>
   <UploadRequest>
       <FolderPath>C:\TestData</FolderPath>
   </UploadRequest>
   ```
   Save as `test.Rdy` in watch folder

2. **Check Logs**
   Look for detection message within 30 seconds

3. **If Not Detected**
   - Enable detailed logging
   - Enable polling
   - Check folder permissions
   - Verify file format

### Automated Test Script
```batch
@echo off
echo ^<?xml version="1.0" encoding="utf-8"?^> > test.Rdy
echo ^<UploadRequest^> >> test.Rdy
echo     ^<FolderPath^>C:\TestData^</FolderPath^> >> test.Rdy
echo ^</UploadRequest^> >> test.Rdy
move test.Rdy "C:\YourWatchFolder\"
echo Test file created. Check service logs for detection.
```

## Performance Considerations

### FileSystemWatcher vs Polling

**FileSystemWatcher (Preferred):**
- ✅ Real-time detection
- ✅ Low CPU usage
- ✅ Immediate response
- ❌ May not work on network drives
- ❌ Can miss events under high load

**Polling (Backup):**
- ✅ Works on network drives
- ✅ Reliable detection
- ✅ Handles high-volume scenarios
- ❌ Higher CPU usage
- ❌ Detection delay (polling interval)

### Recommended Settings
- **Local drives**: FileSystemWatcher only
- **Network drives**: Both methods enabled
- **High volume**: Polling with short interval
- **Debugging**: Both methods with detailed logging
