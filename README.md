# WinSCP Multithreaded Folder Upload Service

A Windows Service that monitors a folder for XML trigger files (*.Rdy) and uploads folder contents specified in the XML to an SFTP server using multithreading for optimal performance.

## Features

### Core Functionality
- **XML Trigger-based Upload**: Monitors for `*.Rdy` XML files that contain folder paths to upload
- **Intelligent XML Parsing**: Automatically detects folder paths in XML using configurable or common element/attribute names
- **Multithreaded Processing**: Configurable concurrent upload limits for optimal performance
- **Flexible Folder Upload**: Uploads files from any folder path specified in the XML trigger file
- **File Filtering**: Configurable include/exclude patterns for file selection
- **Wait Period**: Configurable delay before upload starts (ensures file stability)

### Advanced Features
- **Dual File Detection**: FileSystemWatcher + optional polling for maximum reliability
- **Enhanced Diagnostics**: Comprehensive folder validation and permission checking
- **Network Drive Support**: Polling mode for reliable detection on network paths
- **3-Level Retry Logic**: Configurable retry attempts with exponential backoff
- **Folder Structure Preservation**: Option to maintain folder hierarchy on remote server
- **Intelligent Directory Creation**: Automatically creates target folders on remote server
- **Pre-Creation Optimization**: Creates all directories before uploads for better performance
- **Thread-Safe Directory Operations**: Prevents race conditions during concurrent uploads
- **Automatic Cleanup**: Option to delete trigger files after successful upload
- **Robust Error Handling**: Comprehensive logging and error recovery
- **Graceful Shutdown**: Ensures all uploads complete before service stops

## Configuration

### Basic SFTP Settings
```xml
<add key="FtpHost" value="your-sftp-server.com" />
<add key="FtpUsername" value="your-username" />
<add key="FtpPassword" value="your-password" />
<add key="FtpRemotePath" value="/upload/" />
<add key="SshFingerprint" value="ssh-rsa 2048 xx:xx:xx:xx..." />
```

### Folder Upload Settings
```xml
<!-- File patterns to include (semicolon or comma separated) -->
<add key="IncludeFilePatterns" value="*.pdf;*.txt;*.xml;*.csv;*.xlsx" />

<!-- File patterns to exclude (semicolon or comma separated) -->
<add key="ExcludeFilePatterns" value="*.Rdy;*.tmp;*.log;*.bak;*~" />

<!-- Whether to delete the trigger file after successful upload -->
<add key="DeleteTriggerFileAfterUpload" value="true" />

<!-- Whether to preserve folder structure on remote server -->
<add key="PreserveFolderStructure" value="false" />
```

### XML Parsing Settings
```xml
<!-- Element name containing the folder path -->
<add key="XmlFolderPathElement" value="FolderPath" />

<!-- Attribute name containing the folder path (leave empty to use element) -->
<add key="XmlFolderPathAttribute" value="" />
```

### Directory Creation Settings
```xml
<!-- Whether to pre-create all directories before starting uploads -->
<add key="PreCreateDirectories" value="true" />

<!-- Whether to create directories recursively (parent directories first) -->
<add key="CreateDirectoriesRecursively" value="true" />
```

### Performance Settings
```xml
<!-- Maximum number of concurrent uploads -->
<add key="MaxConcurrentUploads" value="5" />

<!-- Wait time before upload starts (in minutes) -->
<add key="WaitMinutesBeforeUpload" value="2" />
```

### Retry Settings
```xml
<!-- Maximum number of retry attempts (0 = no retries, 3 = 4 total attempts) -->
<add key="MaxRetryAttempts" value="3" />

<!-- Initial delay between retries in seconds -->
<add key="RetryDelaySeconds" value="5" />

<!-- Enable exponential backoff (delay doubles with each retry) -->
<add key="EnableRetryExponentialBackoff" value="true" />
```

### Failure Handling Settings
```xml
<!-- Stop processing new uploads after retry exhaustion (recommended: false) -->
<add key="StopProcessingOnRetryExhaustion" value="false" />

<!-- Stop the entire service on critical failures (recommended: false) -->
<add key="StopServiceOnCriticalFailure" value="false" />

<!-- Maximum consecutive failures before triggering failure handling -->
<add key="MaxConsecutiveFailures" value="5" />
```

## How It Works

1. **File Detection**: Service monitors the configured folder for `*.Rdy` XML files
2. **XML Parsing**: When a trigger file is detected, parses the XML to extract the folder path
3. **Folder Scanning**: Scans the folder specified in the XML for files to upload
4. **File Filtering**: Applies include/exclude patterns to determine which files to upload
5. **Wait Period**: Waits for the configured time to ensure file stability
6. **Retry Logic**: Automatically retries failed operations with exponential backoff
7. **Multithreaded Upload**: Uploads all matching files concurrently (up to the limit)
8. **Cleanup**: Optionally deletes the trigger file after successful upload

## XML Trigger File Format

The service supports flexible XML formats. It will automatically detect folder paths using:

### Method 1: Configured Element Name
```xml
<?xml version="1.0" encoding="utf-8"?>
<UploadRequest>
    <FolderPath>C:\Data\Batch001</FolderPath>
    <Timestamp>2024-01-15T10:30:00</Timestamp>
</UploadRequest>
```

### Method 2: Configured Attribute Name
```xml
<?xml version="1.0" encoding="utf-8"?>
<UploadRequest path="C:\Data\Batch001">
    <Timestamp>2024-01-15T10:30:00</Timestamp>
</UploadRequest>
```

### Method 3: Auto-Detection
If no specific element/attribute is configured, the service will automatically search for common names:
- **Elements**: FolderPath, Path, Directory, Folder, SourcePath, UploadPath
- **Attributes**: path, folder, directory, source, upload

## Example Usage

### Scenario 1: Simple Folder Upload
1. Place files in `C:\Data\Batch001\`
2. Create XML trigger file `batch001.Rdy`:
   ```xml
   <?xml version="1.0" encoding="utf-8"?>
   <Upload>
       <FolderPath>C:\Data\Batch001</FolderPath>
   </Upload>
   ```
3. Drop the trigger file in the monitored folder
4. Service parses XML, uploads all matching files from `C:\Data\Batch001\`
5. Trigger file is deleted after successful upload

### Scenario 2: Preserving Folder Structure
With `PreserveFolderStructure=true`:
- Local: `C:\Upload\Batch001\Documents\file.pdf`
- Remote: `/upload/Documents/file.pdf`

With `PreserveFolderStructure=false`:
- Local: `C:\Upload\Batch001\Documents\file.pdf`
- Remote: `/upload/file.pdf`

## Installation

1. Compile the service
2. Install as Windows Service: `sc create SftpFileUploaderService binPath="path\to\service.exe"`
3. Configure App.config with your settings (includes log4net configuration)
4. Create logs directory: `mkdir logs` (in service directory)
5. Start the service: `sc start SftpFileUploaderService`

## Logging Configuration

The service uses log4net configured directly in App.config:
- **Automatic setup**: No separate log4net.config file needed
- **Multiple outputs**: File logs, error logs, and Windows Event Log
- **Rolling files**: Automatic log rotation when size limits are reached
- **Configurable levels**: Adjust logging verbosity as needed

For detailed logging configuration, see `LOG4NET_APP_CONFIG_GUIDE.md`.

## Monitoring

The service uses log4net for comprehensive logging with multiple outputs:
- **File Logs**: `logs\SftpFileUploader.log` (all activities)
- **Error Logs**: `logs\SftpFileUploader_Errors.log` (warnings and errors only)
- **Windows Event Log**: Application log under "SftpFileUploaderService"

Monitor for:
- File detection events
- Upload progress and retry attempts
- Error conditions and failure patterns
- Performance metrics and consecutive failures

## Performance Considerations

- **MaxConcurrentUploads**: Balance between speed and server load (recommended: 3-10)
- **File Size**: Large files may benefit from lower concurrency
- **Network Bandwidth**: Adjust concurrency based on available bandwidth
- **Server Limits**: Respect SFTP server connection limits

## Troubleshooting

### File Detection Issues

**Problem**: Service starts but doesn't detect new *.Rdy files

**Solutions**:
1. **Enable detailed logging**:
   ```xml
   <add key="EnableDetailedLogging" value="true" />
   ```

2. **For network drives, enable polling**:
   ```xml
   <add key="EnablePolling" value="true" />
   <add key="PollingIntervalSeconds" value="30" />
   ```

3. **Check logs for**:
   - `"Watch folder validation successful"`
   - `"FileSystemWatcher configured for"`
   - `"Detected trigger file via [method]"`

4. **Common issues**:
   - Incorrect watch folder path
   - Permission issues
   - Network drive limitations
   - Files created by applications that don't trigger events

**Test file detection**:
Use the included `TestFileDetection.exe` utility or manually create a test file:
```xml
<?xml version="1.0" encoding="utf-8"?>
<UploadRequest>
    <FolderPath>C:\TestData</FolderPath>
</UploadRequest>
```

For detailed troubleshooting, see `FILE_DETECTION_TROUBLESHOOTING.md`.
